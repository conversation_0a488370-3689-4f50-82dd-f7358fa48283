import { Star } from 'lucide-react';

import { useEffect, useState } from 'react';

function TestimonialRating({ rating, className, size, color }) {
  const [starsColor, setStarsColor] = useState(color);
  useEffect(() => {
    setStarsColor(color);
  }, [color]);
  return (
    <div className={`star-rating mb-3 flex items-center ${className}`}>
      {[...Array(5)].map((star, index) => {
        index += 1;
        const finalColor = index <= rating ? (color ? `${starsColor}` : '#FBBE24') : '#D1D5DB';
        return (
          <span
            key={index}
            style={{
              color: finalColor,
            }}
            className="mr-px"
          >
            <Star size={size || 16} fill={finalColor} />
          </span>
        );
      })}
    </div>
  );
}

export default TestimonialRating;
