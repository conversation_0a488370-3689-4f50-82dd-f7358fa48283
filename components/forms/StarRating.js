import { useEffect, useState } from 'react';
import { Star } from 'lucide-react';

function StarRating({ rating, onRating, disabled, color, rtl, size = 33 }) {
  const [customRating, setCustomRating] = useState(rating);
  const [hover, setHover] = useState(0);
  const [starsColor, setStarsColor] = useState(color);
  useEffect(() => {
    setStarsColor(color);
  }, [color]);

  useEffect(() => {
    setCustomRating(rating);
    onClickRating(customRating);
  }, []);

  const onClickRating = (totalRating) => {
    if(onRating) {
      onRating(totalRating);
    }
  };

  return (
    <div className="star-rating">
      {[...Array(5)].map((star, index) => {
        index += 1;
        return (
          <button
            disabled={disabled}
            type="button"
            key={index}
            style={{
              color: index <= (hover || customRating) ? `${starsColor || '#FBBE24'}` : '#D1D5DB',
            }}
            onClick={() => {
              setCustomRating(index);
              onClickRating(index);
            }}
            onMouseEnter={() => setHover(index)}
            onMouseLeave={() => setHover(customRating)}
          >
            <Star size={size} className={`${rtl ? 'ml-1' : 'mr-1'} fill-current`} />
          </button>
        );
      })}
    </div>
  );
}

export default StarRating;
