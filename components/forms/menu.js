import {
  Paintbrush,
  Heart,
  Star,
  UserRoundPlus,
  Languages,
  Settings,
  Sparkles,
  HeartHandshake,
} from 'lucide-react';

const menuItems = [
  {
    sectionTitle: 'PAGES',
    items: [
      {
        id: 1,
        formStep: 1,
        title: 'Welcome',
        icon: <HeartHandshake size={24} />,
      },
      {
        id: 2,
        formStep: 2,
        title: 'Testimonial',
        icon: <Star size={24} />,
      },
      {
        id: 3,
        formStep: 3,
        title: 'Personal details',
        icon: <UserRoundPlus size={24} />,
      },
      {
        id: 4,
        formStep: 4,
        title: 'Thank you',
        icon: <Heart size={24} />,
      },
    ],
  },
  {
    sectionTitle: 'SETTINGS',
    items: [
      {
        id: 5,
        title: 'Design',
        icon: <Paintbrush size={24} />,
      },
      {
        id: 6,
        title: 'Language',
        icon: <Languages size={24} />,
      },
      {
        id: 7,
        title: 'Automation',
        icon: <Sparkles size={24} />,
      },
      {
        id: 8,
        title: 'General',
        icon: <Settings size={24} />,
      },
    ],
  },
];

export default menuItems;
