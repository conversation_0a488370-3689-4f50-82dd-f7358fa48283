import { useEffect, useState, createContext, useContext } from 'react';
import {
  ArrowLeft,
  Camera,
  Video,
  Copy,
  Circle,
  Square,
  Trash2,
} from 'lucide-react';
import { useForm } from 'react-hook-form';
import { NextSeo } from 'next-seo';
import { useRouter } from 'next/router';
import isUrl from 'validator/lib/isURL';
import mobile from 'is-mobile';
import Head from 'next/head';
import StarRating from './StarRating';
import { testimonialsService } from '../../services';
import ButtonLoading from '../common/ButtonLoading';
import { fileToBase64 } from '../../lib/utils';
import Loading from '../common/Loading';
import useWebcam from '../../lib/useWebcam';
import ContentLoader from '../common/ContentLoader';
import ImageUpload from '../common/ImageUpload';

const FormContext = createContext();

function PublicForm({
  form, preview, showStep, thankYouTab,
}) {
  const { isReady } = useRouter();
  const router = useRouter();
  const startPage = (!form?.welcome?.allowVideo) ? 2 : 1;
  const lastPage = 4;

  const [step, setStep] = useState(startPage);
  const [isSubmittingTestimonial, setIsSubmittingTestimonial] = useState(false);
  const [isVideoTestimonial, setIsVideoTestimonial] = useState(false);
  const [testimonial, setTestimonial] = useState({});
  const [personalDetails, setPersonalDetails] = useState({});
  const [submitError, setSubmitError] = useState('');
  const [leadEmail, setLeadEmail] = useState('');
  const [video, setVideo] = useState(null);
  const [videoPreview, setVideoPreview] = useState(null);
  const [images, setImages] = useState([]);

  const { inviteid, name, email } = router.query;

  useEffect(() => {
    if(showStep) {
      setStep(showStep);
    }
  }, [showStep]);

  if(!isReady) {
    return <Loading background={'bg-transparent'} />;
  }

  const handleTestimonialSubmit = async (formDetails) => {
    setSubmitError('');
    if(preview) {
      return;
    }
    setPersonalDetails(formDetails);
    setLeadEmail(formDetails.email || '');
    const finalTestimonial = { ...testimonial, ...formDetails, images };

    if(finalTestimonial.profileImage && finalTestimonial.profileImage[0] && finalTestimonial.profileImage[0].name) {
      finalTestimonial.profileImage = await fileToBase64(finalTestimonial.profileImage);
    } else {
      finalTestimonial.profileImage = '';
    }
    if(video) {
      finalTestimonial.video = await fileToBase64(video);
    }

    if(finalTestimonial && finalTestimonial.message && finalTestimonial.message.length > 0) {
      finalTestimonial.message = finalTestimonial.message.replace(/\n/g, '<br>');
    }

    setTestimonial(finalTestimonial);
    setIsSubmittingTestimonial(true);

    const { data, error } = await testimonialsService.submitPublicTestimonial({
      testimonial: finalTestimonial,
      formId: form?.publicId,
      inviteId: inviteid,
    });

    if(error) {
      setSubmitError(error);
    } else if(data) {
      setStep(lastPage);
    }
    setIsSubmittingTestimonial(false);
  };

  return (
    <FormContext.Provider
      value={{
        images,
        setImages,
        videoPreview,
        setVideoPreview,
        video,
        setVideo,
        isVideoTestimonial,
        setIsVideoTestimonial,
        step,
        setStep,
        preview,
        form,
        testimonial,
        setTestimonial,
        isSubmittingTestimonial,
        submitError,
        setSubmitError,
        handleTestimonialSubmit,
        name,
        email,
        personalDetails,
        thankYouTab,
      }}
    >
      {!preview && (
        <NextSeo title={'Leave your feedback'} noindex nofollow />
      )}

      <Head>
        <link
          href={`https://fonts.bunny.net/css2?family=${(form?.design?.font || 'Nunito').replace(/ /g, '+')}:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700&display=swap`}
          rel="stylesheet"
        />
      </Head>

      <div
        className={`relative h-full min-h-full overflow-auto bg-transparent ${form.settings.labels.rtl && 'hebrew-font'} `}
        style={{
          backgroundColor: form?.design?.backgroundColor,
          fontFamily: form?.design?.font,
        }}
      >
        <div
          className={`flex h-full ${!preview && 'min-h-screen'} flex-col items-center px-4 py-3 md:pb-8 md:pt-12`}
        >
          <div className="relative max-w-lg my-auto w-full">
            {/* logo */}
            {form?.design?.showLogo && (
              <div className="flex justify-center mb-7">
                <img
                  className="max-h-24 max-w-full inline-block object-cover"
                  src={
                    form?.design?.logo
                    || 'https://cdn.shapo.io/assets/form-placeholder-logo.svg'
                  }
                  alt="Logo"
                  referrerPolicy="no-referrer"
                />
              </div>
            )}

            <div
              className={`justify-stretch relative flex flex-col w-full overflow-hidden px-2 pb-4 pt-4 sm:p-4 rounded-xl border bg-white shadow-xl max-w-lg mt-5 mb-5 ${form.settings.labels.rtl && 'direction-rtl'}`}
            >

              {
                step > startPage && step < lastPage
                && (
                <button
                  type="button"
                  disabled={isSubmittingTestimonial}
                  onClick={(e) => {
                    !preview && setStep(step - 1);
                  }}
                  className="flex"
                >
                  <div
                    className="flex items-center hover:bg-gray-100 rounded-md text-gray-700 font-medium text-sm px-1 pr-2 py-1 ml-1"
                  >
                    <ArrowLeft size={15} className={`${form?.settings?.labels?.rtl && 'rotate-180'}`} />
                    <span className="ml-0.5">{form?.settings?.labels?.backButtonLabel || 'Back'}</span>
                  </div>
                </button>
                )
              }

              {step === 1 && <WelcomeStep />}
              {step === 2 && <TestimonialStep />}
              {step === 3 && <PersonalDetailsStep />}
              {step === 4 && <ThankYouStep />}
            </div>

            {preview ? (
              <>
                {!form?.settings?.hideBranding && (
                  <div className="mx-auto flex select-none justify-center">
                    <ShapoBranding />
                  </div>
                )}
              </>
            ) : (
              <>
                {!form?.settings?.hideBranding && (
                  <>
                    {step === lastPage ? (
                      <div className="mx-auto mt-10 flex select-none flex-col items-center justify-center rounded-lg border border-dashed border-gray-300 bg-white py-5 shadow-sm">
                        <div className="-mb-8">
                          <ShapoBranding />
                        </div>
                        <p className="text-center text-sm !font-bold">You just submitted a testimonial with Shapo :)</p>
                        <p className="mt-1 text-center text-sm font-normal text-gray-800">
                          Want to collect testimonials for your business too? It's free!
                        </p>
                        <div className="mb-1 flex w-full justify-center">
                          <div className="mx-3 mt-3 flex w-full max-w-md flex-col items-center justify-center md:flex-row">
                            <div className="relative mb-2 w-full md:mb-0 md:mr-3">
                              <input
                                id="member_email"
                                value={leadEmail}
                                onChange={(e) => setLeadEmail(e.target.value)}
                                className="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-left text-sm text-gray-900 focus:border-black focus:ring-black"
                                name="email_address"
                                aria-label="Email Address"
                                placeholder="Your email address..."
                                required=""
                                type="email"
                              />
                            </div>
                            <a
                              href={`/signup?email=${leadEmail}&ref=form-lead`}
                              className="w-full cursor-pointer whitespace-nowrap rounded-lg bg-black px-3 py-3 text-center text-sm font-bold text-white hover:opacity-80 md:w-auto lg:px-5"
                            >
                              Get started free
                            </a>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className=" flex justify-center mx-auto select-none">
                        <ShapoBranding />
                      </div>
                    )}
                  </>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </FormContext.Provider>
  );
}

function WelcomeStep() {
  const {
    images,
    setImages,
    step,
    setStep,
    preview,
    form,
    setIsVideoTestimonial,
    setVideo,
  } = useContext(FormContext);
  useEffect(() => {
    if(images.length > 0) {
      setImages([]);
    }
  }, []);
  return (
    <div className="w-full max-w-lg space-y-5 p-3">
      <div>
        <h1 className="text-base font-extrabold md:text-2xl" style={{ color: form?.design?.titleColor }}>
          {form?.welcome?.title || 'How would you like to leave us a testimonial?'}
        </h1>
        <p
          className="mt-6 text-lg"
          style={{ color: form?.design?.textColor }}
          dangerouslySetInnerHTML={{
            __html: form?.welcome?.text || 'You can either write it out as text or record it as a video.',
          }}
        />
      </div>
      <div className="space-y-2">
        {form.welcome.allowVideo && (
        <div>
          <button
            onClick={async (e) => {
              if(!preview) {
                setStep(step + 1);
                setIsVideoTestimonial(true);
              }
            }}
            style={{
              backgroundColor: form?.design?.buttonColor,
              color: form?.design?.buttonTextColor,
            }}
            className="flex h-12 w-full items-center justify-center rounded-lg bg-rose-500 p-2 text-lg font-extrabold text-white hover:opacity-90 disabled:opacity-20"
          >
            <span className="m-1.5">
              <Video size={20} />
            </span>
            {form?.settings?.labels?.recordVideoButtonText || 'Record a video'}
          </button>
        </div>
        )}

        <div>
          <button
            onClick={(e) => {
              if(!preview) {
                setStep(step + 1);
              }
              setVideo(null);
              setIsVideoTestimonial(false);
            }}
            style={{
              backgroundColor: !form.welcome.allowVideo ? form?.design?.buttonColor : '#6b7280',
              color: !form.welcome.allowVideo ? form?.design?.buttonTextColor : '#ffffff',
            }}
            className="flex h-12 w-full items-center justify-center rounded-lg bg-gray-500 p-2 text-lg font-extrabold text-white hover:opacity-90 disabled:opacity-20"
          >
            {form?.settings?.labels?.writeTestimonialButtonText || 'Write a testimonial'}
          </button>
        </div>
      </div>
    </div>
  );
}

function TestimonialStep() {
  const {
    images,
    setImages,
    step,
    setStep,
    preview,
    form,
    testimonial,
    setTestimonial,
    isVideoTestimonial,
    videoPreview,
    setVideoPreview,
    video,
    setVideo,
  } = useContext(FormContext);
  const [error, setError] = useState(null);

  const {
    WebcamComponent,
    startCapture,
    stopCapture,
    capturing,
    recordedBlob,
    previewUrl,
    duration,
    resetCapture,
    isCameraLoading,
    countdown,
    showCountdown,
    hasPermissions,
  } = useWebcam({
    captureLimitInSeconds: 300,
    countDownInSeconds: 3,
    permissionAutoStart: !mobile() && !preview && isVideoTestimonial,
  });

  useEffect(() => {
    if(recordedBlob) {
      setVideo(recordedBlob);
    }
    if(previewUrl) {
      setVideoPreview(previewUrl);
    }
  }, [previewUrl]);

  return (
    <div className="w-full max-w-lg space-y-5 p-3">
      <div>
        <h1 className="text-base font-extrabold md:text-2xl" style={{ color: form?.design?.titleColor }}>
          {form?.testimonial?.title || 'Do you love using our product?'}
        </h1>
        <p
          className="mt-6 text-lg"
          style={{ color: form?.design?.textColor }}
          dangerouslySetInnerHTML={{
            __html:
                form?.testimonial?.text
                || "We'd really appreciate hearing your thoughts on your recent experience with our product, what you like about it and why you'd recommend it. It means a lot to us!",
          }}
        />
      </div>
      {form?.testimonial?.requireRating && (
      <div className="">
        <StarRating
          rating={testimonial.rating}
          onRating={(rate) => setTestimonial({ ...testimonial, rating: rate })}
          color={form?.design?.starsColor}
          rtl={form?.settings?.labels?.rtl}
        />
      </div>
      )}

      {form?.welcome?.allowVideo && preview && (
      <div
        className="pointer-events-none flex flex-col items-center justify-center bg-gray-50"
        style={{ borderRadius: '0.75rem', overflow: 'hidden' }}
      >
        <img src={'https://cdn.shapo.io/assets/webcam-preview.png'} alt={'webcam preview'} />
      </div>
      )}

      {isVideoTestimonial && !video && !mobile() && (
      <>
        {hasPermissions === null && (
        <div className="flex justify-center">
          <ContentLoader text={'Waiting for camera permissions...'} />
        </div>
        )}
        {hasPermissions === false && (
        <div className="flex flex-col justify-center rounded-xl border border-dashed border-red-500 p-8 text-center">
          <Camera className="mx-auto text-red-500" size={40} />
          <p className="pt-3 text-base text-gray-700">
            Seems like we can't access your camera.
            <br />
            Please enable camera access and reload this page.
          </p>
        </div>
        )}
        <div
          className={`relative h-[20rem] ${isCameraLoading && 'hidden'}`}
          style={{ borderRadius: '0.75rem', overflow: 'hidden' }}
        >
          {WebcamComponent}
          {!showCountdown && (
          <div
            className={'absolute left-2 top-2 flex items-center rounded-full bg-black pr-2 text-sm text-white'}
            dir="ltr"
          >
            <div className={`${capturing && 'animate-pulse'} mx-1`}>
              <Circle color={'red'} size={15} fill="red" />
            </div>
            <span className="mt-0.5">{duration}</span>
            {/* <span>{minutes}</span>:<span>{duration >= 10 ? captureDuration : '0' + captureDuration}</span> */}
            {/* {!!timerSeconds && <span className='absolute px-2 top-[7.5rem] left-[11.5rem] text-[100px] opacity-60'>{timerSeconds}</span>} */}
          </div>
          )}

          <button
            className="absolute bottom-4 flex w-full justify-center rounded-full text-sm text-rose-500 opacity-80 hover:opacity-100"
            onClick={async () => {
              if(capturing) {
                await stopCapture();
              } else {
                setError('');
                await startCapture();
              }
            }}
            disabled={showCountdown}
          >
            {showCountdown ? null : capturing ? (
              <Square
                size={50}
                className="rounded-full border-[3px] border-white p-2"
                color="red"
                fill="red"
                aria-label="Stop Recording"
              />
            ) : (
              <Circle
                size={50}
                className="rounded-full border-[3px] border-white p-1"
                color="red"
                aria-label="Start Recording"
                fill="red"
              />
            )}
          </button>
          {!mobile() && showCountdown && !capturing && (
          <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 transform text-[100px] text-white opacity-60">
            {countdown}
          </div>
          )}
        </div>
      </>
      )}
      {isVideoTestimonial && video && videoPreview && (
      <div className={'relative'}>
        <video
          className="rounded-xl"
          src={videoPreview}
          playsInline
          controls
          onDurationChange={(e) => {
            const videoElement = e.target;
            setError(null);
            if(videoElement?.duration !== Infinity) {
              if(videoElement?.duration > 300) {
                setError('Video is too long, the max. duration is 5 minutes');
              } else if(videoElement?.duration < 3) {
                setError('Video is too short, the min. duration is 3 seconds');
              }
            }
          }}
        />
        {!mobile() && (
        <button
          className="absolute right-2 top-2 rounded-full bg-white p-1 text-sm text-rose-500 hover:opacity-80 hover:drop-shadow-lg"
          onClick={() => {
            setError('');
            resetCapture();
            setVideo(null);
            setVideoPreview(null);
          }}
        >
          <Trash2 size={22} className="rounded-xl text-red-600" />
        </button>
        )}
      </div>
      )}
      {isVideoTestimonial && mobile() && (
      <label
        htmlFor="videoFile"
        className="block h-12 min-w-full rounded-lg bg-rose-500 p-2 text-center text-lg font-extrabold text-white hover:opacity-90 disabled:opacity-20"
        style={{
          backgroundColor: form?.design?.buttonColor,
          color: form?.design?.buttonTextColor,
        }}
      >
        {form?.settings.labels?.openCameraText || 'Open Camera'}
        <input
          type="file"
          id="videoFile"
          capture="environment"
          accept="video/*"
          className="hidden"
          onChange={(e) => {
            const file = e.target.files[0];
            if(file) {
              setVideoPreview(URL.createObjectURL(file));
              setVideo(file);
            }
          }}
        />
      </label>
      )}
      {error && <p className="mt-4 text-center text-sm font-bold text-rose-500">{error}</p>}
      {isVideoTestimonial && !videoPreview && mobile() && (
      <div className="text-center text-sm font-semibold">Max. video duration is 5 minutes</div>
      )}
      {(!isVideoTestimonial || (isVideoTestimonial && hasPermissions) || mobile()) && (
      <div>
        <textarea
          value={testimonial.message}
          onChange={(e) => setTestimonial({ ...testimonial, message: e.target.value })}
          name="message"
          placeholder={form?.settings?.labels?.messagePlaceholder || 'Write your feedback here...'}
          className={`${form.welcome.allowVideo ? 'h-32' : 'h-40'} mt-2 block w-full appearance-none rounded-md border border-gray-300 p-2.5 shadow-sm focus:border-black focus:ring-black`}
        />
      </div>
      )}

      <div>
        {!isVideoTestimonial && form?.testimonial?.allowImages && (
        <div className="mb-5">
          <ImageUpload
            images={images}
            setImages={setImages}
            title={form?.settings?.labels?.uploadImagesText}
            mobile={mobile()}
          />
        </div>
        )}

        <button
          onClick={(e) => {
            !preview && setStep(step + 1);
          }}
          disabled={
              error
              || (isVideoTestimonial
                && (!video
                  || (!testimonial.rating && form?.testimonial?.requireRating)
                  || (testimonial.rating === 0 && form?.testimonial?.requireRating)))
              || (!isVideoTestimonial
                && (!testimonial.message
                  || testimonial.message.trim().length === 0
                  || (!testimonial.rating && form?.testimonial?.requireRating)
                  || (testimonial.rating === 0 && form?.testimonial?.requireRating)))
            }
          style={{
            backgroundColor: form?.design?.buttonColor,
            color: form?.design?.buttonTextColor,
          }}
          className="h-12 w-full rounded-lg bg-rose-500 p-2 text-lg font-extrabold text-white hover:opacity-90 disabled:opacity-20"
        >
          {form?.settings?.labels?.submitButtonText || 'Submit'}
        </button>
      </div>
    </div>
  );
}
function PersonalDetailsStep() {
  const {
    step, submitError, setSubmitError, form, handleTestimonialSubmit, isSubmittingTestimonial, name, email,
  } = useContext(FormContext);
  const {
    register,
    handleSubmit,
    reset,
    watch,
    setValue,
    formState: { errors },
  } = useForm({ mode: 'all' });
  const profileImage = watch('profileImage');

  useEffect(() => {
    if(name || email) {
      reset({ name, email });
    }
    setSubmitError('');
  }, []);

  return (
    <div className="w-full max-w-lg space-y-5 p-3">
      <div>
        <h1 className="text-base font-extrabold md:text-2xl" style={{ color: form?.design?.titleColor }}>
          {form?.personalDetails?.title || 'One more thing... 😇'}
        </h1>
        <p
          className="mt-5 pb-2 text-lg"
          style={{ color: form?.design?.textColor }}
          dangerouslySetInnerHTML={{
            __html:
                form?.personalDetails?.text
                || "We'd love to know who's behind this feedback! Please fill in the following details.",
          }}
        />
      </div>
      <div>
        <form className="space-y-4" onSubmit={handleSubmit(handleTestimonialSubmit)}>
          <div className="flex flex-col space-y-4 md:flex-row md:justify-between md:gap-3 md:space-y-0">
            <div className="w-full">
              <label htmlFor="fullName" className="block text-sm font-medium text-gray-500">
                <div className="flex items-center">
                  {form?.settings?.labels?.fullNameLabel || 'Full name'}{' '}
                  <span className="pl-0.5 font-bold text-red-600">*</span>
                </div>
              </label>
              <div className="mt-1 flex w-full flex-col">
                <input
                  name="fullName"
                  {...register('name', {
                    required: 'Name is required',
                  })}
                  type="text"
                  placeholder={form?.settings?.labels?.fullNamePlaceholder || 'John Smith'}
                  className={`block flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 shadow-sm focus:border-black focus:ring-black disabled:opacity-60 ${errors.name && 'border-red-400 focus:border-red-500 focus:ring-red-500'}`}
                />

                {errors && errors.name && <p className="mt-1 text-xs text-red-500">{errors.name.message}</p>}
              </div>
            </div>

            <div className="w-full">
              <label htmlFor="email" className="block text-sm font-medium text-gray-500">
                <div className="flex items-center">
                  {form?.settings?.labels?.emailLabel || 'Email'}{' '}
                  <span className="pl-0.5 font-bold text-red-600">*</span>
                </div>
              </label>
              <div className="mt-1 flex w-full flex-col rounded-md">
                <input
                  name="email"
                  {...register('email', {
                    required: 'Email is required',
                    pattern: /^[a-zA-Z0-9.!#$%&’*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/,
                  })}
                  type="email"
                  placeholder={form?.settings?.labels?.emailPlaceholder || '<EMAIL>'}
                  className={`block flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 shadow-sm focus:border-black focus:ring-black disabled:opacity-60 ${errors.email && 'border-red-400 focus:border-red-500 focus:ring-red-500'}`}
                />
                {errors && errors.email && <p className="mt-1 text-xs text-red-500">{errors.email.message}</p>}
              </div>
            </div>
          </div>

          {form?.personalDetails?.jobTitle?.active && (
          <div className="w-full">
            <label htmlFor="jobTitle" className="block text-sm font-medium text-gray-500">
              <div className="flex items-center">{form?.settings?.labels?.jobTitleLabel || 'Job title'}</div>
            </label>
            <div className="mt-1 flex w-full rounded-md shadow-sm">
              <input
                name="jobTitle"
                {...register('title', { required: false })}
                type="text"
                placeholder={form?.settings?.labels?.jobTitlePlaceholder || 'ex. Head of Marketing'}
                className={`block flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 focus:border-black focus:ring-black disabled:opacity-60 ${errors.jobTitle && 'border-red-400'}`}
              />
            </div>
          </div>
          )}

          {form?.personalDetails?.company?.active && (
          <div className="w-full">
            <label htmlFor="company" className="block text-sm font-medium text-gray-500">
              <div className="flex items-center">{form?.settings?.labels?.companyLabel || 'Company'}</div>
            </label>
            <div className="mt-1 flex w-full rounded-md shadow-sm">
              <input
                name="company"
                {...register('company', { required: false })}
                type="text"
                placeholder={form?.settings?.labels?.companyPlaceholder || 'ex. HubSpot'}
                className={`block flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 focus:border-black focus:ring-black disabled:opacity-60 ${errors.company && 'border-red-400'}`}
              />
            </div>
          </div>
          )}

          {form?.personalDetails?.website?.active && (
          <div className="w-full">
            <label htmlFor="website" className="block text-sm font-medium text-gray-500">
              <div className="flex items-center">{form?.settings?.labels?.websiteLabel || 'Website'}</div>
            </label>
            <div className="mt-1 flex w-full rounded-md shadow-sm">
              <input
                name="website"
                {...register('link', {
                  required: false,
                  validate: (value) => value === ''
                        || isUrl(value, {
                          protocols: ['http', 'https'],
                          require_tld: true,
                        })
                        || 'Please enter a valid URL',
                })}
                type="text"
                placeholder={form?.settings?.labels?.websitePlaceholder || 'https://company.com'}
                className={`block flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 focus:border-black focus:ring-black ${
                  errors.link ? 'border-red-400' : ''
                }`}
              />{' '}
            </div>
            {errors && errors.link && <p className="mt-1 text-xs text-red-500">{errors.link.message}</p>}
          </div>
          )}

          {form?.personalDetails?.profileImage?.active && (
          <div className="flex w-full flex-col">
            <label htmlFor="profileImage" className="block text-sm font-medium text-gray-500">
              <div className="flex items-center">
                {form?.settings?.labels?.profilePictureLabel || 'Profile Picture'}
              </div>
            </label>
            <div className="mt-1 flex items-center rounded-md">
              <div className="group flex w-full flex-row-reverse items-center justify-end rounded-md">
                {profileImage && profileImage[0] && profileImage[0].name && (
                <button
                  onClick={(e) => {
                    e.preventDefault();
                    setValue('profileImage', null);
                  }}
                  className="ml-1 cursor-pointer rounded-md p-1.5 px-3 text-center text-sm font-medium text-red-500 underline hover:opacity-75"
                >
                  Remove
                </button>
                )}
                <label className="m-3 cursor-pointer rounded-md border border-gray-300 p-1.5 px-3 text-center text-sm font-medium text-gray-500 shadow hover:opacity-75">
                  <input {...register('profileImage')} accept="image/*" type="file" className="hidden" />
                  {form?.settings?.labels?.pickImageButtonText || 'Pick an image'}
                </label>
                <img
                  className="h-14 w-14 rounded-full border border-gray-300 object-cover p-0.5 shadow"
                  src={
                        profileImage && profileImage[0] && profileImage[0].name
                          ? window.URL.createObjectURL(profileImage[0])
                          : 'https://cdn.shapo.io/assets/avatar-placeholder.png'
                      }
                />
              </div>
            </div>
          </div>
          )}

          <div className="pt-1">
            {submitError && <p className="pb-2 text-center font-bold text-red-500">{submitError}</p>}

            <ButtonLoading
              isLoading={isSubmittingTestimonial}
              disabled={Object.keys(errors).length > 0 || isSubmittingTestimonial}
              type={'submit'}
              size={30}
              className="mt-2 flex h-12 w-full items-center justify-center rounded-lg bg-rose-500 p-2 text-lg font-extrabold text-white hover:opacity-90 disabled:opacity-20"
              style={{
                backgroundColor: form?.design?.buttonColor,
                color: form?.design?.buttonTextColor,
              }}
            >
              {form?.settings?.labels?.doneButtonText || 'Done'}
            </ButtonLoading>

            <p className="mt-3 px-2 text-center text-xs tracking-tight text-gray-400">
              {form?.settings?.labels?.marketingConsent
                  || 'By submitting your feedback, you agree to our terms, privacy policy, and grant permission for its use across social channels and in our marketing efforts.'}
            </p>
          </div>
        </form>
      </div>
    </div>
  );
}

function ThankYouStep() {
  const {
    step, preview, form, testimonial, thankYouTab,
  } = useContext(FormContext);
  const [copied, setCopied] = useState(false);
  const [displayContent, setDisplayContent] = useState(null);
  useEffect(() => {
    if(preview) {
      setDisplayContent(thankYouTab === 'positive' ? form?.thankYou : form?.thankYou?.negative);
    } else if(form?.thankYou?.ratingBased) {
      const userRating = testimonial?.rating;
      const negativeThreshold = form?.thankYou?.negative?.rating;
      setDisplayContent((userRating && (userRating < negativeThreshold)) ? form?.thankYou?.negative : form?.thankYou);
    } else {
      setDisplayContent(form?.thankYou);
    }
  }, [thankYouTab, form?.thankYou]);

  const copyText = (e) => {
    navigator.clipboard.writeText(testimonial.message.replace(/<br\s*\/?>/gi, '\n')).then(() => {
      setCopied(true);
    });
  };

  return (
    <div className="w-full max-w-lg space-y-5 p-3 py-4">
      <div
        style={{
          backgroundImage: 'url(https://cdn.shapo.io/assets/check-animation.gif)',
        }}
        className="-my-5 mx-auto -mb-3 h-32 select-none bg-contain bg-center bg-no-repeat"
      />
      <div>
        <h1 className="text-center text-base font-extrabold md:text-2xl" style={{ color: form?.design?.titleColor }}>
          {displayContent?.title || 'Thanks a ton!'}
        </h1>
        <p
          className="mt-6 text-center text-lg"
          style={{ color: form?.design?.textColor }}
          dangerouslySetInnerHTML={{
            __html:
              displayContent?.text
              || "We're thrilled to hear about your experience and truly appreciate your feedback. Your kind words mean the world to us and keep our team motivated.",
          }}
        />
      </div>

      {!preview && testimonial.message && (
        <div className="relative rounded-md border border-dashed bg-gray-50 p-3 text-sm italic text-gray-600">
          <p dangerouslySetInnerHTML={{ __html: testimonial.message }} />
          <button
            className={`absolute drop-shadow ${form?.settings?.labels?.rtl ? 'left-1.5' : 'right-1.5'} bottom-1.5 rounded-md border bg-white p-2 hover:opacity-75 ${copied && 'border-green-200 bg-green-50 text-green-500'}`}
            onClick={copyText}
          >
            <Copy size={16} />
          </button>
        </div>
      )}

      {displayContent?.cta?.active && (
        <div className="flex w-full">
          <a
            href={displayContent?.cta?.url}
            style={{
              backgroundColor: form?.design?.buttonColor,
              color: form?.design?.buttonTextColor,
            }}
            className="block flex h-12 w-full items-center justify-center rounded-lg bg-rose-500 p-2 text-lg font-extrabold text-white hover:opacity-90 disabled:opacity-20"
          >
            {displayContent?.cta?.text}
          </a>
        </div>
      )}
    </div>
  );
}

function ShapoBranding() {
  return (
    <a
      style={{ fontFamily: 'Nunito' }}
      href={'https://shapo.io?ref=form-branding'}
      target="_blank"
      className="flex mx-auto flex-items justify-center mb-10 rounded-full px-2.5 py-1 mx-auto bg-white group"
      rel="noopener"
    >
      <span className="text-gray-600 text-sm font-medium group-hover:opacity-75">
        Powered by
      </span>
      <img
        className="ml-1 h-5 w-16 group-hover:opacity-75"
        src="https://cdn.shapo.io/assets/logo-sm.png"
      />
    </a>
  );
}

export default PublicForm;
