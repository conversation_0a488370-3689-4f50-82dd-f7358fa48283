import Router from 'next/router';
import NProgress from 'nprogress';
import { Toaster } from 'react-hot-toast';

import Analytics from 'analytics';
import googleTagManager from '@analytics/google-tag-manager';
import { AnalyticsProvider } from 'use-analytics';
import { IntercomProvider } from 'react-use-intercom';
import { useEffect } from 'react';
import shapoTracker from '../../../lib/analyticsTracker';

let analytics = null;

if(process.env.NEXT_PUBLIC_ENABLE_THIRD_PARTIES === 'true') {
  analytics = Analytics({
    app: 'shapo',
    plugins: [
      googleTagManager({
        containerId: 'GTM-TZBSG5N5',
      }),
    ],
  });
} else {
  analytics = {
    track: () => {},
    page: () => {},
    identify: () => {},
  };
}

Router.events.on('routeChangeStart', () => NProgress.start());
Router.events.on('routeChangeComplete', (url) => {
  NProgress.done();
});
Router.events.on('routeChangeError', () => NProgress.done());

function InternalLayout({ children }) {
  useEffect(() => {
    shapoTracker.init({
      mixpanelToken: process.env.NEXT_PUBLIC_MIXPANEL_KEY,
      posthogKey: process.env.NEXT_PUBLIC_POSTHOG_KEY,
    });
  }, []);

  return (
    <AnalyticsProvider instance={analytics}>
      <IntercomProvider appId={'dpfkdvaq'} autoBoot={process.env.NEXT_PUBLIC_ENABLE_THIRD_PARTIES === 'true'}>
        <Toaster toastOptions={{ duration: 4000 }} position="top-center" />
        {children}
      </IntercomProvider>
    </AnalyticsProvider>
  );
}

export default InternalLayout;
