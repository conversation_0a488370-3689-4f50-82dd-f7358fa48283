import { useState } from 'react';
import { trackUIAction, trackAPICall, trackDBOperation, log, captureException } from '../../lib/sentry';

/**
 * Example component demonstrating proper Sentry usage
 * This shows how to implement error tracking and performance monitoring
 */
function SentryExampleComponent({ userId }) {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState(null);

  // Example: UI Action with Performance Tracking
  const handleUpdateProfile = async () => {
    return trackUIAction(
      "Profile Update Button Click",
      async (span) => {
        setLoading(true);
        
        // Add context to the span
        span.setAttribute("user.id", userId);
        span.setAttribute("form.fields", 5);
        span.setAttribute("validation.enabled", true);
        
        try {
          // Simulate form validation
          log.debug("Starting profile validation", { userId });
          
          // Simulate API call with tracking
          const result = await trackAPICall(
            "PUT",
            `/api/users/${userId}`,
            async (apiSpan) => {
              apiSpan.setAttribute("user.id", userId);
              apiSpan.setAttribute("cache.enabled", false);
              
              // Simulate API call
              const response = await fetch(`/api/users/${userId}`, {
                method: 'PUT',
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ name: "Updated Name" })
              });
              
              apiSpan.setAttribute("http.status_code", response.status);
              
              if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
              }
              
              return response.json();
            },
            { operation: "profile_update" }
          );
          
          setData(result);
          log.info("Profile updated successfully", { 
            userId, 
            duration: "1.2s",
            fieldsUpdated: 5 
          });
          
          return result;
        } catch (error) {
          log.error("Profile update failed", {
            userId,
            errorMessage: error.message,
            retryAttempt: 1
          });
          throw error;
        } finally {
          setLoading(false);
        }
      },
      { userId, action: "profile_update" }
    );
  };

  // Example: Database Operation Tracking
  const handleFetchUserData = async () => {
    return trackDBOperation(
      "SELECT",
      "users",
      async (span) => {
        span.setAttribute("user.id", userId);
        span.setAttribute("query.type", "findUnique");
        
        try {
          // Simulate database query
          log.trace("Executing user query", { 
            query: "SELECT * FROM users WHERE id = ?", 
            params: [userId] 
          });
          
          // Simulate database call
          const result = await new Promise((resolve) => {
            setTimeout(() => resolve({ id: userId, name: "John Doe" }), 100);
          });
          
          span.setAttribute("db.rows_affected", result ? 1 : 0);
          
          log.debug("User data retrieved", { 
            userId, 
            executionTime: "45ms",
            cacheHit: false 
          });
          
          return result;
        } catch (error) {
          log.error("Database query failed", {
            userId,
            table: "users",
            operation: "SELECT",
            errorCode: error.code
          });
          throw error;
        }
      },
      { userId, queryType: "user_lookup" }
    );
  };

  // Example: Error Boundary-style Error Handling
  const handleRiskyOperation = async () => {
    try {
      // Simulate an operation that might fail
      if (Math.random() > 0.5) {
        throw new Error("Random failure occurred");
      }
      
      log.info("Risky operation completed successfully", { userId });
    } catch (error) {
      // Capture exception with rich context
      captureException(error, {
        tags: {
          section: "risky-operations",
          userId: userId,
          feature: "example-component"
        },
        extra: {
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent,
          url: window.location.href
        },
        user: {
          id: userId,
          segment: "premium"
        }
      });
      
      // Log the error with structured data
      log.error("Risky operation failed", {
        userId,
        errorMessage: error.message,
        stackTrace: error.stack,
        context: "example-component"
      });
      
      // Re-throw if needed for UI handling
      throw error;
    }
  };

  // Example: Performance Warning
  const handleSlowOperation = async () => {
    const startTime = Date.now();
    
    try {
      // Simulate slow operation
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const duration = Date.now() - startTime;
      
      if (duration > 1000) {
        log.warn("Slow operation detected", {
          operation: "slow-example",
          duration: `${duration}ms`,
          threshold: "1000ms",
          userId
        });
      }
    } catch (error) {
      log.error("Slow operation failed", {
        userId,
        duration: `${Date.now() - startTime}ms`,
        errorMessage: error.message
      });
    }
  };

  return (
    <div className="p-4 space-y-4">
      <h2 className="text-xl font-bold">Sentry Integration Examples</h2>
      
      <div className="space-y-2">
        <button
          onClick={handleUpdateProfile}
          disabled={loading}
          className="px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
        >
          {loading ? 'Updating...' : 'Update Profile (UI Action + API Call)'}
        </button>
        
        <button
          onClick={handleFetchUserData}
          className="px-4 py-2 bg-green-500 text-white rounded"
        >
          Fetch User Data (Database Operation)
        </button>
        
        <button
          onClick={handleRiskyOperation}
          className="px-4 py-2 bg-yellow-500 text-white rounded"
        >
          Risky Operation (Error Handling)
        </button>
        
        <button
          onClick={handleSlowOperation}
          className="px-4 py-2 bg-purple-500 text-white rounded"
        >
          Slow Operation (Performance Warning)
        </button>
      </div>
      
      {data && (
        <div className="mt-4 p-2 bg-gray-100 rounded">
          <pre>{JSON.stringify(data, null, 2)}</pre>
        </div>
      )}
    </div>
  );
}

export default SentryExampleComponent;
