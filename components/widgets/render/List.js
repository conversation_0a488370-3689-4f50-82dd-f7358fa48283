import dynamic from 'next/dynamic';
import Avatar from 'react-avatar';
import { LoaderCircle, RotateCw, PlayIcon, Star, PauseIcon } from 'lucide-react';
import { forwardRef, useEffect, useRef, useState } from 'react';
import { useRouter } from 'next/router';
import isRtlText from 'is-rtl-text';
import isMobile from 'is-mobile';
import { getShadowSize } from '../WidgetConstants';
import widgetsService from '../../../services/widgetsService';
import { transformMessage, isDarkColor } from '../../../lib/utils';

const Branding = dynamic(() => import('./Branding'));
const FormLink = dynamic(() => import('./FormLink'));
const TestimonialRating = dynamic(() => import('../../forms/TestimonialRating'));
const SourceIcon = dynamic(() => import('../../testimonials/SourceIcon'));
const ReadMoreBlock = dynamic(() => import('../ReadMoreBlock'));
const VideoPlayer = dynamic(() => import('../../common/VideoPlayer'));
const Images = dynamic(() => import('../Images'));

function triggerResizeEvent() {
  const event = new Event('resize');
  // Dispatch the event on the window
  window.dispatchEvent(event);
}

const VideoTestimonial = forwardRef(({ widget, testimonial, pauseAll }, ref) => {
  const formattedDate = new Date(testimonial.date).toLocaleString('en-us', {
    month: 'short',
    year: 'numeric',
    day: 'numeric',
  });
  const isRtlName = isRtlText(testimonial.name);
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);
  const videoRef = useRef(null);
  const [showOverlay, setShowOverlay] = useState(true);

  const handleTogglePlayPause = () => {
    if(!videoRef.current) {
      return;
    }
    if(isVideoPlaying) {
      videoRef.current.pause();
      setIsVideoPlaying(false);
      setShowOverlay(true);
    } else {
      pauseAll();
      videoRef.current.play();
      setIsVideoPlaying(true);
    }
  };

  const handleMouseEnter = () => {
    setShowOverlay(true);
  };

  const handleMouseLeave = () => {
    if(isVideoPlaying) {
      setShowOverlay(false);
    }
  };

  return (
    <div
      ref={ref}
      className={`mb-4 flex flex-col rounded-xl border ${isRtlName && 'direction-rtl'} ${getShadowSize(widget.design.shadowSize)}`}
      style={{
        backgroundColor: widget?.design?.cardColor,
        borderColor: widget.design.borderColor,
        zoom: widget.design.cardSize || 1,
      }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onClick={handleTogglePlayPause}
    >
      <div className="relative">
        <VideoPlayer
          autoPlay={false}
          videoRef={videoRef}
          containerclassname={`${testimonial.message && testimonial.message.length > 0 ? 'rounded-t-xl' : 'rounded-xl'}`}
          className={`${testimonial.message && testimonial.message.length > 0 ? 'rounded-t-xl' : 'rounded-xl'} h-72 w-full`}
          src={`https://stream.mux.com/${testimonial.video?.playbackId}.m3u8`}
          poster={`https://image.mux.com/${testimonial.video?.playbackId}/thumbnail.png`}
          onPause={() => {
            setIsVideoPlaying(false);
            setShowOverlay(true);
          }}
        />
        <div
          className={`transition-opacity duration-500 ease-in-out ${showOverlay ? 'opacity-100 pointer-events-auto' : 'opacity-0 pointer-events-none'} z-20`}
        >
          <div className="absolute inset-0" style={{ pointerEvents: 'none' }}>
            <div
              className="bg-gradient-to-b from-transparent to-black"
              style={{
                height: '100%',
                borderRadius: !testimonial.message && '0 0 10px 10px',
                opacity: (isVideoPlaying && isMobile ? 0.6 : 1),
                transition: 'opacity 0.5s ease-in-out',
              }}
            />
            <div
              className={`absolute items-center  ${isRtlName ? 'left-3' : 'right-3'}  bottom-4 p-1 cursor-pointer hover:bg-white hover:bg-opacity-5 rounded-md`}
              onClick={handleTogglePlayPause}
              style={{ transform: 'translateZ(1px)', willChange: 'transform' }}
            >
              {isVideoPlaying
                ? <PauseIcon color="white" fill={'white'} size={28} />
                : <PlayIcon color="white" fill={'white'} size={28} />}
            </div>
          </div>
          <div className={`absolute flex items-center ${isRtlName ? 'right-3' : 'left-3'} bottom-3`}>
            <div>
              {widget?.design?.showProfileImage && (
              <div className={`relative h-10 w-10 flex-shrink-0 ${isRtlName ? 'ml-3' : 'mr-3'}`}>
                {testimonial.profileImage && testimonial.profileImage.length > 0 ? (
                  <img
                    referrerPolicy={'no-referrer'}
                    className="h-full w-full rounded-full object-cover"
                    src={testimonial.profileImage}
                    alt={testimonial.name}
                  />
                ) : (
                  <Avatar
                    className="h-full w-full rounded-full object-cover"
                    textSizeRatio={2}
                    size={40}
                    name={testimonial.name}
                  />
                )}
              </div>
              )}
            </div>
            <div>
              <div className="flex flex-col -space-y-2 font-bold">
                {testimonial.rating && widget.design.showRating && (
                <div>
                  <TestimonialRating size={16} rating={testimonial.rating} color={widget?.design?.starsColor} />
                </div>
                )}

                <div className="text-white">
                  <p className="text-md font-bold tracking-tight">{testimonial.name}</p>
                  {widget.design.showTagline && (testimonial.title || testimonial.company) && (
                  <div className="flex flex-row">
                    <p className="text-xs tracking-tight transition duration-150 ease-in-out line-clamp-1">
                      {testimonial.title && testimonial.company
                        ? `${testimonial.title}, ${testimonial.company}`
                        : testimonial.title && !testimonial.company
                          ? testimonial.title
                          : testimonial.company}
                    </p>
                  </div>
                  )}
                  {testimonial.date && widget.design.showDate && !testimonial.message && (
                  <div className="text-xs text-gray-300">{formattedDate}</div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {testimonial.message && (
        <div className="p-6">
          <blockquote
            style={{ color: widget.design.textColor }}
            className={`${
              isRtlText(testimonial.message)
                ? 'direction-rtl hebrew-font text-right'
                : 'direction-ltr text-left font-medium' // font weight moved here due to hebrew looking bold
            } flex-grow whitespace-normal break-words text-sm text-gray-700 sm:text-base`}
          >
            {widget?.design?.showReadMore ? (
              <ReadMoreBlock onClick={() => triggerResizeEvent()} labels={widget?.labels} settings={widget?.design}>
                {transformMessage(testimonial.message)}
              </ReadMoreBlock>
            ) : (
              <div
                dangerouslySetInnerHTML={{
                  __html: transformMessage(testimonial.message),
                }}
              />
            )}
          </blockquote>
          {testimonial.date && widget.design.showDate && (
            <div className="mt-3 text-xs text-gray-600">{formattedDate}</div>
          )}
        </div>
      )}
    </div>
  );
});

const TextTestimonial = forwardRef(({ widget, testimonial }, ref) => {
  const formattedDate = new Date(testimonial.date).toLocaleString('en-us', {
    month: 'short',
    year: 'numeric',
    day: 'numeric',
  });
  const isRtlName = isRtlText(testimonial.name);
  return (
    <div
      ref={ref}
      className={`mb-4 flex flex-col rounded-xl border p-5 ${getShadowSize(widget.design.shadowSize)} ${isRtlName && 'direction-rtl hebrew-font'}`}
      style={{
        backgroundColor: widget?.design?.cardColor,
        borderColor: widget.design.borderColor,
        zoom: widget.design.cardSize || 1,
      }}
    >
      <div className={'relative mb-3 flex flex-row'}>
        {widget.design.showSource && !widget?.design?.showProfileImage && testimonial.source !== 'text' && testimonial.source !== 'importedVideo' && (
          <div
            className={`absolute ${isRtlText(testimonial.name) ? '-left-2' : '-right-2'} -top-2 ${testimonial.source === 'twitter' && isDarkColor(widget?.design?.cardColor) && 'brightness-0 invert'}`}
          >
            <SourceIcon
              size={6}
              source={testimonial.source}
              link={testimonial.link}
              clickable={widget?.design?.enableLink}
            />
          </div>
        )}
        {widget?.design?.showProfileImage && (
          <div className={`relative h-[50px] w-[50px] flex-shrink-0 ${isRtlName ? 'ml-5' : 'mr-5'}`}>
            {widget.design.showSource && widget?.design?.showProfileImage && testimonial.source !== 'text' && testimonial.source !== 'importedVideo' && (
              <div
                className={`absolute bg-white rounded-full shadow-xl ${isRtlText(testimonial.name) ? '-left-0.5' : '-right-0.5'} -bottom-1.5 ${testimonial.source === 'twitter' && isDarkColor(widget?.design?.cardColor) && 'brightness-0 invert'}`}
              >
                <SourceIcon
                  size={6}
                  source={testimonial.source}
                  link={testimonial.link}
                  clickable={widget?.design?.enableLink}
                />
              </div>
            )}
            {testimonial.profileImage && testimonial.profileImage.length > 0 ? (
              <img
                referrerPolicy={'no-referrer'}
                className="h-full w-full rounded-full object-cover shadow-md"
                src={testimonial.profileImage}
                alt={testimonial.name}
              />
            ) : (
              <Avatar
                className="h-full w-full rounded-full object-cover shadow-md"
                textSizeRatio={2}
                size={50}
                name={testimonial.name}
              />
            )}
          </div>
        )}
        <div className="flex flex-grow flex-col">
          <div className="flex flex-col mt-1" style={{ color: widget?.design?.titleColor }}>
            <p className="text-md font-bold">{testimonial.name}</p>
            {widget.design.showTagline && (testimonial.title || testimonial.company) && (
              <div className="flex flex-row">
                <p className="text-xs tracking-tight transition duration-150 ease-in-out mb-1 -mt-0.5 line-clamp-1">
                  {testimonial.title && testimonial.company
                    ? `${testimonial.title}, ${testimonial.company}`
                    : testimonial.title && !testimonial.company
                      ? testimonial.title
                      : testimonial.company}
                </p>
              </div>
            )}
            {testimonial.date && widget.design.showDate && (
              <div className="text-xs font-medium text-gray-500 tracking-tight" style={{ color: widget?.design?.dateColor }}>
                {formattedDate}
              </div>
            )}

            <blockquote
              style={{ color: widget.design.textColor }}
              className={`${
                isRtlText(testimonial.message)
                  ? 'direction-rtl hebrew-font text-right'
                  : 'direction-ltr text-left font-medium'
              } flex-grow whitespace-normal break-words text-sm text-gray-700 sm:text-base mt-5`}
            >
              {testimonial.rating && widget.design.showRating && (
              <TestimonialRating className={'mb-2'} size={16} rating={testimonial.rating} color={widget?.design?.starsColor} />
              )}
              {widget?.design?.showReadMore ? (
                <ReadMoreBlock onClick={() => triggerResizeEvent()} settings={widget?.design} labels={widget?.labels}>
                  {transformMessage(testimonial.message)}
                </ReadMoreBlock>
              ) : (
                <div
                  dangerouslySetInnerHTML={{
                    __html: transformMessage(testimonial.message),
                  }}
                />
              )}
            </blockquote>

            {widget?.design?.showImages && testimonial?.images?.length > 0 && (
              <div className="mt-4">
                <Images size={20} redirect images={testimonial.images} rtl={isRtlText(testimonial.message)} />
              </div>
            )}
          </div>
        </div>
      </div>

    </div>
  );
});

function List({
  widget,
  testimonials: baseTestimonials,
  hasNextPage,
  totals,
  preview,
  forceBranding,
  previewParams,
}) {
  const [testimonials, setTestimonials] = useState(baseTestimonials);
  const [showLoadMoreButton, setShowLoadMoreButton] = useState(widget?.settings?.showLoadMore && hasNextPage);
  const [loadMorePage, setLoadMorePage] = useState(1);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const router = useRouter();
  const [isPreview, setIsPreview] = useState(preview);
  const isRtlReviewText = isRtlText(widget?.labels?.reviewsText);
  const listComponentRed = useRef();

  useEffect(() => {
    setTestimonials(baseTestimonials);
    setLoadMorePage(1);
    setShowLoadMoreButton(widget?.settings?.showLoadMore && hasNextPage);
  }, [baseTestimonials]);

  const loadMoreTestimonials = ({}) => {
    setIsLoadingMore(true);
    widgetsService
      .getPublicWidget({
        preview: isPreview,
        publicWidgetId: widget.publicId,
        pageURL: router.query.url,
        page: loadMorePage + 1,
        previewParams,
      })
      .then((res) => {
        setTestimonials([...testimonials, ...res.testimonials]);
        setLoadMorePage(loadMorePage + 1);
        if(!res.hasMore) {
          setShowLoadMoreButton(false);
        }
        setIsLoadingMore(false);
      })
      .catch((err) => {
        setIsLoadingMore(false);
      });
  };

  const pauseAll = () => {
    const videos = listComponentRed.current.querySelectorAll('video');
    if(videos.length > 0) {
      videos.forEach((video) => {
        if(!video.paused) {
          video.pause();
        }
      });
    }
  };

  return (
    <div className="mx-auto w-full" ref={listComponentRed} style={{ backgroundColor: widget?.design?.backgroundColor }}>

      <div className="mx-auto max-w-3xl">

        <div className={`flex items-center justify-between ${isRtlReviewText && 'direction-rtl hebrew-font'}`}>
          {widget?.settings?.showTotals && widget?.settings?.showTotals !== 'none' && (
          <ListReviewsHeader totals={totals} widget={widget} />
          )}

          {!widget?.settings?.hideBranding
            && <Branding floating={forceBranding} customText={'Powered by'} source={'widget-branding'} />}
        </div>

        {testimonials.map((testimonial) => {
          if(testimonial.video && widget.design.showVideos) {
            return <VideoTestimonial widget={widget} testimonial={testimonial} key={testimonial._id} pauseAll={pauseAll} />;
          }
          return <TextTestimonial widget={widget} testimonial={testimonial} key={testimonial._id} />;
        })}

        {showLoadMoreButton && (
          <div className="flex items-center justify-center pb-5">
            <button
              onClick={() => loadMoreTestimonials()}
              disabled={isLoadingMore}
              className={`${isLoadingMore ? '' : 'px-6'} flex items-center space-x-2 rounded-full border border-gray-300 bg-white p-2 font-semibold text-gray-800 drop-shadow-md hover:opacity-80`}
            >
              {isLoadingMore && <LoaderCircle size={20} className="animate-spin" />}
              {!isLoadingMore && (
                <>
                  <RotateCw size={20} />
                  <span>{widget?.labels?.loadMoreText || 'Load more'}</span>
                </>
              )}
            </button>
          </div>
        )}

        {widget?.connectedForm?.formPublicId && (
          <div className="pb-5">
            <FormLink connectedForm={widget?.connectedForm} />
          </div>
        )}

      </div>
    </div>
  );
}

function ListReviewsHeader({ totals, widget }) {
  const isRtlReviewText = isRtlText(widget?.labels?.reviewsText);

  function formatDecimalString(str) {
    const num = parseFloat(str);
    return num % 1 === 0 ? num.toString() : num.toFixed(1).replace(/\.0$/, '');
  }

  return (
    <div
      className={'flex flex-col items-start justify-center mt-8 mb-8'}
    >
      <div className="flex items-center">
        <div className={'flex items-center text-sm font-semibold text-gray-500'}>

          <div
            className={`${isRtlReviewText ? 'pl-3' : 'pr-3'} flex font-extrabold text-5xl`}
            style={{
              color: widget?.design?.totals?.textColor || '#000000',
            }}
          >
            {formatDecimalString(totals.rating)}
          </div>
          <div>
            <div className={'flex items-center leading-none mb-0.5'}>
              {[...Array(5)].map((_, i) => {
                const starValue = i + 1;
                const isFullStar = starValue <= Math.floor(totals.rating) || (totals.rating >= 4.85 && starValue === 5);
                const isHalfStar = !isFullStar && totals.rating % 1 >= 0.5 && Math.ceil(totals.rating) === starValue;

                let fillColor = '#D1D5DB';
                let strokeColor = '#D1D5DB';

                if(isFullStar) {
                  fillColor = widget?.design?.totals?.starsColor || '#fbbe24';
                  strokeColor = widget?.design?.totals?.starsColor || '#fbbe24';
                }
                if(isHalfStar) {
                  fillColor = 'url(#halfStarGradient)';
                  strokeColor = widget?.design?.totals?.starsColor || '#fbbe24';
                }
                return (
                  <Star
                    key={i}
                    fill={fillColor}
                    strokeWidth={2}
                    stroke={strokeColor}
                    size={19}
                  />

                );
              })}
              <svg width="0" height="0">
                <defs>
                  <linearGradient id="halfStarGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="50%" stopColor={widget?.design?.totals?.starsColor || '#fbbe24'} />
                    <stop offset="50%" stopColor={`${widget?.design?.totals?.starsColor || '#fbbe24'}60`} />
                  </linearGradient>
                </defs>
              </svg>
            </div>

            <div
              className={'flex items-center'}
              style={{
                color: widget?.design?.totals?.textColor || '#000000',
              }}
            >
              <span className={'opacity-80 font-semibold text-base !tracking-tight'}>
                {totals.total} {widget?.labels?.reviewsText || 'reviews'}
              </span>
            </div>
          </div>

        </div>
      </div>

    </div>
  );
}

List.displayName = 'List';
export default List;
