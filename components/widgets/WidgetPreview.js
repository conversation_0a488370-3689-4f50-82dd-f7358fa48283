import { debounce } from 'lodash';
import { useEffect } from 'react';
import useSWR from 'swr';
import Link from 'next/link';
import { Download, LoaderCircleIcon, Plus } from 'lucide-react';
import { widgetsService } from '../../services';
import ContentLoader from '../common/ContentLoader';
import WidgetRenderer from './render/WidgetRenderer';
import { shouldRenderWidget } from '../../lib/utils';

function WidgetPreview({ widget, isLoading }) {
  if(!widget || isLoading) {
    return (
      <div className="relative h-screen overflow-auto bg-white">
        <div className="flex h-full flex-col items-center px-4 py-3 md:pb-8">
          <div className="relative my-auto w-full max-w-full">
            <ContentLoader text={'Loading preview...'} />
          </div>
        </div>
      </div>
    );
  }

  const previewParams = {
    settings: {
      numTestimonials: widget?.settings?.numTestimonials,
      minRating: widget?.settings?.minRating,
      sortBy: widget?.settings?.sortBy,
      tags: widget?.settings?.tags,
      keywords: widget?.settings?.keywords,
      showLoadMore: widget?.settings?.showLoadMore,
      showTotals: widget?.settings?.showTotals,
    },
    design: {
      showVideos: widget?.design?.showVideos,
      showFirstNameOnly: widget?.design?.showFirstNameOnly,
    },
    type: widget?.type,
  };

  const { data, error, mutate, isValidating } = useSWR(`/widgets/${widget.publicId}`, () => widgetsService.getPublicWidget({
    publicWidgetId: widget?.publicId,
    preview: true,
    previewParams,
  }));

  useEffect(() => {
    const debouncedRevalidate = debounce(async () => {
      await mutate();
    }, 200);
    debouncedRevalidate();
    return () => {
      debouncedRevalidate.cancel();
    };
  }, [JSON.stringify(previewParams)]);

  if(!data && !error) {
    return (
      <div className="relative h-screen overflow-auto bg-white">
        <div className="flex h-full flex-col items-center px-4 py-3 md:pb-8">
          <div className="relative my-auto w-full max-w-full">
            <ContentLoader text={'Loading preview...'} />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={'relative h-screen overflow-auto bg-white'}>
      {
        ((data || error) && isValidating) && <div className="absolute right-2 top-2"><LoaderCircleIcon className="animate-spin" /></div>
      }
      <div className="flex h-full flex-col items-center">
        <div className="relative my-auto w-full max-w-full">
          {error && (
            <div className="text-center">
              <div>There was an issue loading the preview:</div>
              <p className="text-center font-bold text-red-500">{error}</p>
            </div>
          )}

          {!shouldRenderWidget(data) && (
            <div className="mx-auto max-w-lg text-center">
              <img className="mx-auto mb-5 w-96" src="https://cdn.shapo.io/assets/testimonials-import.png" />
              <div className="mb-2 text-lg font-bold">No testimonials to show yet.</div>
              <div>
                Start by importing your existing testimonials from sources like Google, Trustpilot, etc, or create a
                form to collect testimonials from your customers :)
              </div>
              <div className="mt-6 flex items-center justify-center space-x-2.5">
                <Link href={`/${widget?.workspaceId}/testimonials`}>
                  <a className="flex w-auto items-center justify-center rounded-md bg-black py-2 pl-3 pr-4 font-medium text-white hover:opacity-80">
                    <Download className="mr-2" size={22} />
                    <span className="">Import Testimonials</span>
                  </a>
                </Link>
                <Link href={`/${widget?.workspaceId}/forms`}>
                  <a className="flex w-auto items-center justify-center rounded-md bg-black py-2 pl-3 pr-4 font-medium text-white hover:opacity-80">
                    <Plus className="mr-2" size={22} />
                    <span className="">Create a form</span>
                  </a>
                </Link>
              </div>
            </div>
          )}
          {!error && shouldRenderWidget(data) && (
            <WidgetRenderer
              preview
              totals={data?.totals}
              hasNextPage={data?.hasMore}
              widget={widget}
              testimonials={data?.testimonials}
              previewParams={previewParams}
            />
          )}
        </div>
      </div>
    </div>
  );
}

export default WidgetPreview;
