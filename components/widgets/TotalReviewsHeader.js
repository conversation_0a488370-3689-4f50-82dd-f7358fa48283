import { Star } from 'lucide-react';
import isRtlText from 'is-rtl-text';

function TotalReviewsHeader({ totals, widget, compact }) {
  const isRtlOutOfLabel = isRtlText(widget?.labels?.outOfText);
  const isRtlReviewText = isRtlText(widget?.labels?.reviewsText);

  function formatDecimalString(str) {
    const num = parseFloat(str);
    return num % 1 === 0 ? num.toString() : num.toFixed(1).replace(/\.0$/, '');
  }

  return (
    <div
      className={`mx-auto flex flex-col items-center justify-center ${compact ? 'mt-2 rounded-full bg-white bg-opacity-100 px-2 py-1 pr-2.5 shadow-lg' : 'mb-4 pb-4 pt-5'}`}
    >
      <div className="flex items-center">
        <div className={`flex items-center ${compact ? 'flex-row' : 'flex-col'} text-sm font-semibold text-gray-500`}>
          <div className={`flex items-center ${compact ? 'mb-0' : 'mb-2'} leading-none`}>
            {[...Array(5)].map((_, i) => {
              const starValue = i + 1;
              const isFullStar = starValue <= Math.floor(totals.rating) || (totals.rating >= 4.85 && starValue === 5);
              const isHalfStar = !isFullStar && totals.rating % 1 >= 0.5 && Math.ceil(totals.rating) === starValue;

              let fillColor = '#D1D5DB';
              let strokeColor = '#D1D5DB';

              if(isFullStar) {
                fillColor = widget?.design?.totals?.starsColor || '#fbbe24';
                strokeColor = widget?.design?.totals?.starsColor || '#fbbe24';
              }
              if(isHalfStar) {
                fillColor = 'url(#halfStarGradient)';
                strokeColor = widget?.design?.totals?.starsColor || '#fbbe24';
              }
              return (
                <Star
                  key={i}
                  fill={fillColor}
                  strokeWidth={2}
                  stroke={strokeColor}
                  size={compact ? 23 : 45}
                />

              );
            })}
            <svg width="0" height="0">
              <defs>
                <linearGradient id="halfStarGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                  <stop offset="50%" stopColor={widget?.design?.totals?.starsColor || '#fbbe24'} />
                  <stop offset="50%" stopColor={`${widget?.design?.totals?.starsColor || '#fbbe24'}60`} />
                </linearGradient>
              </defs>
            </svg>
          </div>

          <div
            className={`flex items-center ${compact ? 'ml-2 text-base' : 'text-base'}`}
            style={{
              color: compact ? widget?.design?.totals?.textColor || '#232323' : widget?.design?.totals?.textColor || '#000000',
            }}
          >
            <div className={`${isRtlOutOfLabel && 'direction-rtl hebrew-font'} flex items-center leading-none`}>
              <span className="pr-1.5 font-extrabold leading-none">{formatDecimalString(totals.rating)}</span>
              <span className="pr-1 font-bold leading-none opacity-70">{widget?.labels?.outOfText || 'out of'} 5</span>
            </div>
            <div className="px-1 text-sm font-semibold opacity-70">|</div>
            <span className={`${isRtlReviewText && 'direction-rtl hebrew-font'} pl-1 font-bold opacity-70`}>
              {totals.total} {widget?.labels?.reviewsText || 'reviews'}
            </span>
          </div>
        </div>
      </div>

      {/* <div className='mt-2'> */}
      {/*    <a href={`https://shapo.io?ref=widget-header`} target='_blank' */}
      {/*       className='flex mx-auto flex-items justify-center px-2.5 py-1 mx-auto group bg-white rounded-xl'> */}
      {/*      <div className='flex items-center direction-ltr group-hover:opacity-75'> */}
      {/*        <span className={`text-gray-600 font-semibold text-sm`}>Powered by</span><img */}
      {/*        className="h-5 ml-1" */}
      {/*        src='https://cdn.shapo.io/assets/logo.png'/> */}
      {/*      </div> */}
      {/*    </a> */}
      {/*  </div> */}
    </div>
  );
}

export default TotalReviewsHeader;
