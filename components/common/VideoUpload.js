import { useState, useRef } from 'react';
import { FileVideo2, Trash2 } from 'lucide-react';
import { fileToBase64 } from '../../lib/utils';

function VideoUpload({ isEditing, video, setVideo, title }) {
  const [error, setError] = useState('');
  const [currentFile, setCurrentFile] = useState({});
  const fileInputRef = useRef(null);

  const handleDivClick = () => {
    fileInputRef.current.click();
  };

  const handleFileChange = async (e) => {
    setError('');
    const file = e.target.files[0];
    if(file) {
      setCurrentFile(file);
      if(file.size <= 52428800) {
        // 50MB limit
        const base64Video = await fileToBase64(file);
        setVideo(base64Video);
      } else {
        setError('Video must be under 50MB to upload');
      }
    }
  };

  const handleFileValidation = async (event) => {
    setError('');
    const file = event.target.files[0];
    const duration = await getVideoDuration(file);
    if(duration && duration < 3) {
      setError('Video is too short, the min. duration is 3 seconds');
      return;
    }
    setCurrentFile(file);
    if(file && !file.type.includes('video/')) {
      setError('Please select a valid video file (MP4, WebM, Ogg, etc).');
      return;
    }
    await handleFileChange(event);
  };

  const handleDeleteVideo = () => {
    setVideo(null);
  };

  const getVideoDuration = (file) => new Promise((resolve, reject) => {
    const video = document.createElement('video');
    video.preload = 'metadata';
    video.onloadedmetadata = () => {
      // Android browser sometimes sets duration to 1 for unknown duration
      if(video.duration === Infinity || video.duration === 1) {
        reject('Duration could not be determined');
      }
      URL.revokeObjectURL(video.src);
      resolve(video.duration);
    };
    video.onerror = () => {
      reject('Error loading video file');
    };
    video.src = URL.createObjectURL(file);
  });

  return (
    <div className="space-y-4">
      {video ? (
        <div className="relative">
          <video
            className="h-80 w-full rounded-lg bg-black object-contain"
            src={video && video.playbackId ? `https://stream.mux.com/${video?.playbackId}.m3u8` : video}
            poster={video && video.playbackId ? `https://image.mux.com/${video?.playbackId}/thumbnail.png` : ''}
            controls
          />
          <p className="mt-2 text-center text-xs font-semibold italic text-gray-600">{currentFile.name}</p>
          {!isEditing && (
            <button
              className="leading-none0 absolute right-2 top-2 flex h-6 items-center rounded-full border border-red-400 bg-red-100 p-1.5 pl-1.5 pr-2 text-sm text-red-500 hover:opacity-90 hover:drop-shadow-lg"
              onClick={handleDeleteVideo}
            >
              <Trash2 size={14} />
              <span className="ml-1 font-medium">Remove</span>
            </button>
          )}
        </div>
      ) : (
        <div>
          <label htmlFor="video-upload" className="mb-2 block font-bold text-gray-800">
            {title || 'Upload a video'}
          </label>
          <div
            className="relative rounded-lg border-2 border-dashed border-gray-300 p-6 hover:cursor-pointer hover:bg-gray-50"
            onClick={handleDivClick}
          >
            <input
              ref={fileInputRef}
              id="video-upload"
              type="file"
              className="hidden"
              accept="video/*"
              onChange={handleFileValidation}
            />
            <div className="text-center">
              <FileVideo2 className="mx-auto text-gray-700" size={28} />
              <p className="mt-1 text-sm text-gray-500">Select a video file up to 50MB</p>
            </div>
            {error && (
              <div className="text-center text-sm">
                <p className="font-medium text-red-500">{error}</p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

export default VideoUpload;
