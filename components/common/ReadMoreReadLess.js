import React from 'react';
import PropTypes from 'prop-types';

class ReactReadMoreReadLess extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      showMore: false,
    };
  }

  render() {
    const { props } = this;
    const {
      children,
      ellipsis,
      readMoreText,
      readLessText,
      readMoreClassName,
      readLessClassName,
      readMoreStyle,
      readLessStyle,
      charLimit,
    } = props;
    const { showMore } = this.state;
    const shortText = children
      .substr(0, charLimit)
      .replace(/[\s\n]+$/, '')
      .replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]+$/, '') + (charLimit >= children.length ? '' : ellipsis);
    const that = this;
    function ReadMore() {
      return charLimit >= children.length ? null : (
        <button
          type="button"
          className={readMoreClassName}
          style={readMoreStyle}
          onClick={(ev) => {
            ev.preventDefault();
            ev.stopPropagation();
            that.setState({ showMore: true });
          }}
        >
          {readMoreText}
        </button>
      );
    }
    function ReadLess() {
      return charLimit >= children.length ? null : (
        <button
          type="button"
          className={readLessClassName}
          style={readLessStyle}
          onClick={(ev) => {
            ev.preventDefault();
            ev.stopPropagation();
            that.setState({ showMore: false });
          }}
        >
          {readLessText}
        </button>
      );
    }
    return (
      <>
        <div dangerouslySetInnerHTML={{ __html: showMore ? children : shortText }} />
        {showMore ? <ReadLess /> : <ReadMore />}
      </>
    );
  }
}

ReactReadMoreReadLess.propTypes = {
  charLimit: PropTypes.number,
  ellipsis: PropTypes.string,
  readMoreText: PropTypes.object,
  readLessText: PropTypes.object,
  readMoreClassName: PropTypes.string,
  readLessClassName: PropTypes.string,
  readMoreStyle: PropTypes.object,
  readLessStyle: PropTypes.object,
  children: PropTypes.string.isRequired,
};
ReactReadMoreReadLess.defaultProps = {
  charLimit: 150,
  ellipsis: '…',
  readMoreText: 'Read more',
  readLessText: 'Read less',
  readMoreClassName: 'react-read-more-read-less react-read-more-read-less-more',
  readLessClassName: 'react-read-more-read-less react-read-more-read-less-less',
  readMoreStyle: { whiteSpace: 'nowrap', textDecoration: 'none' },
  readLessStyle: { whiteSpace: 'nowrap', textDecoration: 'none' },
};
export default ReactReadMoreReadLess;
