import { useCallback, useEffect, useState } from 'react';
import Script from 'next/script';
import axios from 'axios';
import { LoaderCircle, Star, Search, Copy, Check, Code, AlertCircle, RefreshCw } from 'lucide-react';
import Head from 'next/head';
import { NextSeo } from 'next-seo';
import Branding from '../../components/widgets/render/Branding';
import PublicLayout from '../../components/layouts/external/PublicLayout';
import shapoTracker from '../../lib/analyticsTracker';

function GoogleReviewSchemaGenerator({ branding = true }) {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedPlace, setSelectedPlace] = useState(null);
  const [generatedSchema, setGeneratedSchema] = useState(null);
  const [copied, setCopied] = useState(false);

  const fetchPlaces = useCallback(async (searchQuery) => {
    setIsLoading(true);

    if(!searchQuery) {
      setIsLoading(false);
      setResults([]);
      return;
    }

    try {
      const response = await axios.post('/api/search-places', {
        textQuery: searchQuery,
      });
      setResults(response.data.places || []);
      setIsLoading(false);
    } catch(error) {
      console.error('Error fetching places:', error);
      setResults([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      fetchPlaces(query);
    }, 500);

    return () => clearTimeout(debounceTimer);
  }, [query, fetchPlaces]);

  const handleQueryChange = (newQuery) => {
    setIsLoading(true);
    setQuery(newQuery);
  };

  const handleClick = (place) => {
    setSelectedPlace(place);
    generateSchema(place);
    setQuery(`${place.displayName.text}, ${place.formattedAddress}`);
    setResults([]);
    setCopied(false);
    shapoTracker.trackEvent('Mini Tools - Generated google review schema');
  };

  const resetFields = () => {
    setSelectedPlace(null);
    setGeneratedSchema(null);
    setQuery('');
    setResults([]);
    setCopied(false);
  };

  const handleSchemaCopy = () => {
    setCopied(true);
    navigator.clipboard.writeText(generatedSchema);
    setTimeout(() => {
      setCopied(false);
    }, 2000);
  };

  const generateSchema = (place) => {
    // Create JSON-LD schema for the selected business
    const schema = {
      "@context": "https://schema.org",
      "@type": "LocalBusiness",
      "name": place.displayName.text,
      "address": {
        "@type": "PostalAddress",
        "streetAddress": place.formattedAddress
      }
    };

    // Add rating if available
    if (place.rating && place.userRatingCount) {
      schema.aggregateRating = {
        "@type": "AggregateRating",
        "ratingValue": place.rating,
        "ratingCount": place.userRatingCount
      };
    }

    // Add website if available
    if (place.websiteUri) {
      schema.url = place.websiteUri;
    }

    // Format the schema as a script tag
    const formattedSchema = `<script type="application/ld+json">\n${JSON.stringify(schema, null, 2)}\n</script>`;
    setGeneratedSchema(formattedSchema);
  };

  return (
    <>
      <NextSeo noindex />

      <Head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1" />
        <meta name="robots" content="indexifembedded" />
      </Head>
      <Script
        src="https://cdnjs.cloudflare.com/ajax/libs/iframe-resizer/4.3.6/iframeResizer.contentWindow.min.js"
        integrity="sha512-R7Piufj0/o6jG9ZKrAvS2dblFr2kkuG4XVQwStX+/4P+KwOLUXn2DXy0l1AJDxxqGhkM/FJllZHG2PKOAheYzg=="
        crossOrigin="anonymous"
        referrerPolicy="no-referrer"
        strategy="afterInteractive"
      />
      <div className="bg-transparent p-4">
        <div className="mx-auto rounded-xl border border-gray-200 bg-white p-6 shadow-sm">
          <div className="mb-6 border-b border-gray-100 pb-5">
            <h1 className="mb-2 text-2xl font-bold text-gray-800">Google Review Schema Generator</h1>
            <p className="text-gray-600">
              Generate a structured data schema for your Google reviews to display rich results in search engines.
            </p>
          </div>
          
          <form onSubmit={(e) => e.preventDefault()}>
            {selectedPlace ? (
              <div className="flex items-center justify-between mb-10">
                <div>
                  <div className="text-lg font-bold">{selectedPlace.displayName.text}</div>
                  <div className="font-semibold text-gray-500">{selectedPlace.formattedAddress}</div>
                  {selectedPlace.rating && selectedPlace.userRatingCount && (
                    <div className="mt-2 flex items-center space-x-1 text-sm font-semibold leading-none text-gray-800">
                      <Star size={15} className="text-yellow-500 fill-current" />
                      <span className="flex items-center">
                        {selectedPlace.rating} ({selectedPlace.userRatingCount} reviews)
                      </span>
                    </div>
                  )}
                </div>
                <div className="hidden md:block">
                  <button
                    className="flex items-center border-b border-gray-500 font-semibold text-gray-500 hover:opacity-80"
                    onClick={resetFields}
                  >
                    <Search size={15} className="mr-1" />
                    Search another business
                  </button>
                </div>
              </div>
            ) : (
              <div className="flex flex-col md:flex-row">
                <div className="relative flex-grow">
                  <label htmlFor="simple-search" className="sr-only">
                    Search
                  </label>
                  <div className="flex items-center">
                    <input
                      autoComplete={'off'}
                      type="text"
                      id="simple-search"
                      className="block w-full rounded-lg border border-gray-300 bg-gray-50 p-3 pr-10 text-base text-gray-900 focus:border-black focus:ring-black md:rounded-lg"
                      placeholder="Search your Google business name or address..."
                      required
                      readOnly={generatedSchema}
                      value={query}
                      onChange={(e) => handleQueryChange(e.target.value)}
                    />
                    {isLoading && (
                      <LoaderCircle size={20} className="absolute right-4 animate-spin text-gray-900" />
                    )}
                  </div>
                </div>
              </div>
            )}
          </form>
          
          {!generatedSchema ? (
            <div className="">
              <div className="">
                {results.length > 0 && !isLoading ? (
                  <ul className="mt-3 max-h-60 divide-y divide-gray-100 overflow-y-auto rounded-lg border border-gray-200 bg-white shadow-md">
                    {results.map((place) => (
                      <li
                        key={place.id}
                        className="flex cursor-pointer flex-col rounded-lg p-4 transition-colors hover:bg-blue-50"
                        onClick={() => handleClick(place)}
                      >
                        <h3 className="font-semibold">{place.displayName.text}</h3>
                        <p className="text-sm text-gray-600">{place.formattedAddress}</p>
                        {place.rating && place.userRatingCount && (
                          <div className="mt-2 flex items-center space-x-1 text-xs font-semibold leading-none text-gray-800">
                            <Star size={14} className="fill-current text-amber-400" />
                            <span className="flex items-center">
                              {place.rating} <span className="ml-1 text-gray-500">({place.userRatingCount} reviews)</span>
                            </span>
                          </div>
                        )}
                      </li>
                    ))}
                  </ul>
                ) : (
                  results
                  && results.length === 0
                  && query
                  && !isLoading && (
                    <p className="mt-4 rounded-xl border p-4 text-center text-gray-500">
                      We couldn't find this business on Google.
                    </p>
                  )
                )}
              </div>
            </div>
          ) : (
            <div>
              <div className="space-y-6">
                <div className="overflow-hidden rounded-xl border border-blue-200 bg-gradient-to-br from-blue-50 to-blue-100 shadow-sm">
                  <div className="border-b border-blue-200 bg-blue-600 px-6 py-4">
                    <div className="flex items-center">
                      <div className="flex h-10 w-10 items-center justify-center rounded-full bg-white">
                        <Code size={20} className="text-blue-600" />
                      </div>
                      <h3 className="ml-3 text-xl font-bold text-white">Your Google review schema is ready!</h3>
                    </div>
                  </div>
                  
                  <div className="p-6 rounded-lg">
                   
                    <div className="relative rounded-lg border border-gray-200 bg-gray-50 shadow-sm">
                      <div className="flex items-center rounded-lg justify-between border-b border-gray-200 bg-gray-100 px-4 py-2">
                        <div className="flex items-center rounded-lg">
                          <Code size={16} className="mr-2 text-gray-500" />
                          <span className="text-xs font-medium text-gray-600">JSON-LD Schema</span>
                        </div>
                        <button
                          onClick={handleSchemaCopy}
                          className={`flex items-center rounded-md ${copied ? 'bg-green-600' : 'bg-blue-600'} px-3 py-1 text-white transition-colors hover:${copied ? 'bg-green-700' : 'bg-blue-700'}`}
                          title="Copy to clipboard"
                        >
                          <div className="flex items-center space-x-1">
                            {copied ? <Check size={14} /> : <Copy size={14} />}
                            <span className="text-xs font-medium">{copied ? 'Copied!' : 'Copy code'}</span>
                          </div>
                        </button>
                      </div>
                      <pre className="overflow-x-auto rounded-b-lg bg-gray-900 p-5 text-sm leading-relaxed text-gray-100">
                        <code>{generatedSchema}</code>
                      </pre>

                      
                    </div>
                    <div className="rounded-lg mt-5   bg-white p-5">
                  <h3 className="font-medium text-gray-700">What this does:</h3>
                  <ul className="ml-5 mt-2 list-disc text-sm text-gray-600">
                    <li>Adds structured data about your business to your website</li>
                    <li>Helps search engines understand your business information</li>
                    <li>May enhance your search results with star ratings</li>
                    <li>Improves your website's SEO</li>
                  </ul>
                </div>
                  
                  </div>
                </div>
                
               

                <div className="mt-8 space-y-6 rounded-lg border border-gray-200 bg-gray-50 p-5 shadow-sm">
                  <div className="flex">
                    <div className="mr-3 flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-blue-100 text-blue-600">
                      <span className="font-bold">1</span>
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-800">How to implement:</h3>
                      <p className="mt-1 text-sm text-gray-600">
                        Paste this code in your page's <code className="rounded bg-gray-200 px-1.5 py-0.5 text-sm font-mono">&lt;head&gt;</code> section
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex">
                    <div className="mr-3 flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-amber-100 text-amber-600">
                      <AlertCircle size={18} />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-800">Important note:</h3>
                      <p className="mt-1 text-sm text-gray-600">
                        Google doesn't support star ratings on homepages. To display star ratings in search results, add the schema code to a specific product or service page instead of your homepage.
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex">
                    <div className="mr-3 flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-green-100 text-green-600">
                      <span className="font-bold">2</span>
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-800">Next steps:</h3>
                      <p className="mt-1 text-sm font-medium text-gray-700">
                        Ask Google to re-crawl your page(s) and wait
                      </p>
                      <p className="mt-1 text-sm text-gray-600">
                        To help Google notice these changes, you must tell Google to re-crawl the URLs where you've added the schema code. Just head over to your Google Search Console and request URL indexing for the page(s).
                      </p>
                      <div className="mt-2 flex items-center text-sm text-gray-600">
                        <RefreshCw size={14} className="mr-1 text-gray-500" />
                        <p>It might take a few days for your ratings and reviews to start showing up in Google search results.</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
             
            </div>
          )}
        </div>
        {branding && <Branding customText="Made by" source={'google-review-schema-generator'} />}
      </div>
    </>
  );
}

GoogleReviewSchemaGenerator.Layout = PublicLayout;
export default GoogleReviewSchemaGenerator;
