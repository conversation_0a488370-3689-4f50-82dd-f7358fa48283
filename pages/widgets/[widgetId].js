import { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';
import Head from 'next/head';
import { NextSeo } from 'next-seo';
import Script from 'next/script';
import { useRouter } from 'next/router';
import widgetsService from '../../services/widgetsService';
import PublicLayout from '../../components/layouts/external/PublicLayout';
import { shouldRenderWidget } from '../../lib/utils';

const WidgetRenderer = dynamic(() => import('../../components/widgets/render/WidgetRenderer'));
const Branding = dynamic(() => import('../../components/widgets/render/Branding'));
const ContentLoader = dynamic(() => import('../../components/common/ContentLoader'));

function WidgetId({ widgetId, forceBranding }) {
  const router = useRouter();
  const [widgetData, setWidgetData] = useState(null);
  const [loadingWidget, setLoadingWidget] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    setWidgetData(null);
    setError('');
    if(router.isReady && router.query.widgetId) {
      widgetsService
        .getPublicWidget({
          publicWidgetId: widgetId,
          pageURL: router.query.url,
        })
        .then((res) => {
          setWidgetData(res);
          setLoadingWidget(false);
        })
        .catch((err) => {
          setError(err);
          setLoadingWidget(false);
        });
    }
  }, [router.isReady, router.query]);

  return (
    <>
      <NextSeo
        noindex
        additionalMetaTags={[
          {
            name: 'robots',
            content: 'indexifembedded',
          },
        ]}
      />
      <Head>
        <meta charSet="utf-8" />
        <title>Shapo Widget</title>
        <link rel="preconnect" href={process.env.NEXT_PUBLIC_API_BASE} crossOrigin />
        <link rel="preconnect" href="https://cdn.shapo.io" crossOrigin />
        <link rel="preconnect" href="https://cdnjs.cloudflare.com" crossOrigin />
        <link
          rel="iframely resizable"
          type="text/html"
          media="(min-width: 400px) and (min-height: 400px)"
          href={`${process.env.NEXT_PUBLIC_FRONT}/widgets/${widgetId}`}
        />
        <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1" />
      </Head>
      <Script
        src="https://cdnjs.cloudflare.com/ajax/libs/iframe-resizer/4.3.6/iframeResizer.contentWindow.min.js"
        integrity="sha512-R7Piufj0/o6jG9ZKrAvS2dblFr2kkuG4XVQwStX+/4P+KwOLUXn2DXy0l1AJDxxqGhkM/FJllZHG2PKOAheYzg=="
        crossOrigin="anonymous"
        referrerPolicy="no-referrer"
        strategy="afterInteractive"
      />
      {loadingWidget && (
        <div className="relative overflow-hidden bg-transparent">
          <div className="flex h-full flex-col items-center px-4 py-3 md:pb-8">
            <div className="relative my-auto w-full max-w-full">
              <ContentLoader />
            </div>
          </div>
        </div>
      )}

      {/* error */}
      {error && (
        <div className="rounded border border-dashed p-5 pb-0 text-center">
          <div>There was an issue loading the widget:</div>
          <p className="text-center font-bold text-red-500">{error.toString()}</p>
          <Branding />
        </div>
      )}

      {/* no testimonials to show */}
      {!loadingWidget && !shouldRenderWidget(widgetData) && (
        <div className="rounded border border-dashed p-5 pb-0 text-center">
          <div>There was an issue loading the widget:</div>
          <p className="text-center font-bold text-red-500">No testimonials to show</p>
          <Branding />
        </div>
      )}

      {/* has testimonials */}
      {!loadingWidget && shouldRenderWidget(widgetData) && (
        <WidgetRenderer
          forceBranding={forceBranding}
          hasNextPage={widgetData.hasMore}
          widget={widgetData.widget}
          totals={widgetData.totals}
          testimonials={widgetData.testimonials}
        />
      )}
    </>
  );
}

export async function getServerSideProps({ query, res }) {
  const { widgetId, forceBranding } = query;
  return {
    props: JSON.parse(
      JSON.stringify({
        widgetId,
        forceBranding,
      }),
    ),
  };
}

WidgetId.Layout = PublicLayout;
export default WidgetId;
