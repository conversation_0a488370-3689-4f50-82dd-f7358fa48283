import { useState, useEffect, useRef } from 'react';
import { ChevronLeft, Star, Video, Pause, Check, X, Gift, ArrowRight, Camera } from 'lucide-react';

export default function MultiStepTestimonial() {
  // Form state
  const [currentStep, setCurrentStep] = useState('testimonial');
  const [activeTab, setActiveTab] = useState('text');
  const [rating, setRating] = useState(null);
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [feedback, setFeedback] = useState('');
  const [hoverRating, setHoverRating] = useState(null);

  // Animation states
  const [animating, setAnimating] = useState(false);
  const [direction, setDirection] = useState(null);
  const [visibleStep, setVisibleStep] = useState('testimonial');
  const [showConfetti, setShowConfetti] = useState(false);

  // Prize state
  const [wonPrize, setWonPrize] = useState(null);
  const [eligibleForPrize, setEligibleForPrize] = useState(false);

  // User details
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [jobTitle, setJobTitle] = useState('');
  const [company, setCompany] = useState('');
  const [website, setWebsite] = useState('');
  const [profilePicture, setProfilePicture] = useState(null);
  const [profilePreview, setProfilePreview] = useState(null);

  // Validation state
  const [errors, setErrors] = useState({});

  // File input ref
  const fileInputRef = useRef(null);

  // Sample prizes for the wheel
  const prizes = [
    { text: '10% OFF', color: '#FF6B6B', type: 'coupon', value: 'SAVE10' },
    { text: 'Free Trial', color: '#4ECDC4', type: 'link', value: 'https://example.com/free-trial' },
    { text: '25% OFF', color: '#1A535C', type: 'coupon', value: 'SAVE25' },
    { text: 'eBook', color: '#FF9F1C', type: 'link', value: 'https://example.com/ebook' },
    { text: '50% OFF', color: '#7B2CBF', type: 'coupon', value: 'SAVE50' },
    { text: 'Free Course', color: '#2EC4B6', type: 'link', value: 'https://example.com/course' },
    { text: 'Try Again', color: '#E71D36', type: 'coupon', value: 'TRYAGAIN' },
    { text: 'Free Demo', color: '#3D5A80', type: 'link', value: 'https://example.com/demo' },
  ];

  // Check if user is eligible for prize wheel
  useEffect(() => {
    // Example condition: Video testimonial AND rating >= 4
    if(activeTab === 'video' && recordingTime > 0 && rating && rating >= 4) {
      setEligibleForPrize(true);
    } else {
      setEligibleForPrize(false);
    }
  }, [activeTab, recordingTime, rating]);

  // Handle star rating
  const handleRating = (value) => {
    setRating(value);
  };

  // Toggle recording state
  const toggleRecording = () => {
    if(isRecording) {
      setIsRecording(false);
      // In a real app, you would stop recording here
    } else {
      setIsRecording(true);
      setRecordingTime(0);
      // In a real app, you would start recording here
    }
  };

  // Format seconds to MM:SS
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60)
      .toString()
      .padStart(2, '0');
    const secs = (seconds % 60).toString().padStart(2, '0');
    return `${mins}:${secs}`;
  };

  // Recording timer
  useEffect(() => {
    let interval;

    if(isRecording) {
      interval = setInterval(() => {
        setRecordingTime((prev) => prev + 1);
      }, 1000);
    }

    return () => clearInterval(interval);
  }, [isRecording]);

  // Trigger confetti when thank you page is shown
  useEffect(() => {
    if(currentStep === 'thank-you' && !animating) {
      setShowConfetti(true);

      // Hide confetti after it finishes
      const timer = setTimeout(() => {
        setShowConfetti(false);
      }, 4000); // Slightly longer than the confetti duration

      return () => clearTimeout(timer);
    }
  }, [currentStep, animating]);

  // Add a function to handle profile picture selection
  const handleProfilePicture = (e) => {
    if(e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setProfilePicture(file);
      const reader = new FileReader();
      reader.onload = (event) => {
        if(event.target?.result) {
          setProfilePreview(event.target.result);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle wheel spin completion
  const handleSpinComplete = (prize) => {
    setWonPrize(prize);
    transitionToStep('thank-you', 'forward');
  };

  // Validate current step
  const validateStep = () => {
    const newErrors = {};

    if(currentStep === 'testimonial') {
      if(!rating) {
        newErrors.rating = 'Please provide a rating';
      }

      if(activeTab === 'text' && !feedback.trim()) {
        newErrors.feedback = 'Please provide your feedback';
      }

      if(activeTab === 'video' && recordingTime === 0) {
        newErrors.video = 'Please record a video testimonial';
      }
    }

    if(currentStep === 'user-details') {
      if(!name.trim()) {
        newErrors.name = 'Name is required';
      }

      if(!email.trim()) {
        newErrors.email = 'Email is required';
      } else if(!/^\S+@\S+\.\S+$/.test(email)) {
        newErrors.email = 'Please enter a valid email';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle transition between steps
  const transitionToStep = (nextStep, animDirection) => {
    if(animating) {
      return;
    }

    setAnimating(true);
    setDirection(animDirection);

    // After a short delay to allow the exit animation to play
    setTimeout(() => {
      setCurrentStep(nextStep);
      setVisibleStep(nextStep);

      // After the new content is in place, start the enter animation
      setTimeout(() => {
        setAnimating(false);
      }, 50);
    }, 300); // This should match the exit animation duration
  };

  // Handle next step
  const handleNext = () => {
    if(validateStep()) {
      if(currentStep === 'testimonial') {
        transitionToStep('user-details', 'forward');
      } else if(currentStep === 'user-details') {
        // Check if user is eligible for prize wheel
        if(eligibleForPrize) {
          transitionToStep('spin-wheel', 'forward');
        } else {
          transitionToStep('thank-you', 'forward');
        }
      }
    }
  };

  // Handle previous step
  const handlePrevious = () => {
    if(currentStep === 'user-details') {
      transitionToStep('testimonial', 'backward');
    } else if(currentStep === 'spin-wheel') {
      transitionToStep('user-details', 'backward');
    }
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    if(validateStep()) {
      // In a real app, you would submit the form data here
      if(eligibleForPrize) {
        transitionToStep('spin-wheel', 'forward');
      } else {
        transitionToStep('thank-you', 'forward');
      }
    }
  };

  // Helper function to conditionally join class names
  const cn = (...classes) => classes.filter(Boolean).join(' ');

  // Get animation classes based on state
  const getAnimationClasses = () => {
    if(!animating) {
      return 'opacity-100 translate-x-0';
    }

    if(direction === 'forward') {
      return 'animate-slide-out-left';
    } if(direction === 'backward') {
      return 'animate-slide-out-right';
    }

    return 'opacity-100 translate-x-0';
  };

  // Render progress indicator
  const renderProgress = () => {
    const steps = [
      { id: 'testimonial', label: 'Testimonial' },
      { id: 'user-details', label: 'Your Details' },
      { id: 'thank-you', label: 'Complete' },
    ];

    // Insert spin wheel step if eligible
    if(eligibleForPrize) {
      steps.splice(2, 0, { id: 'spin-wheel', label: 'Prize' });
    }

    return (
      <div className="flex items-center justify-center py-8 px-6">
        {steps.map((step, index) => (
          <div key={step.id} className="flex items-center">
            <div
              className={cn(
                'flex items-center justify-center w-10 h-10 rounded-full text-sm font-medium transition-all duration-300 shadow-sm',
                currentStep === step.id
                  ? 'bg-gradient-to-r from-violet-600 to-indigo-600 text-white scale-110 shadow-md'
                  : currentStep === 'thank-you'
                  || (currentStep === 'spin-wheel' && step.id !== 'thank-you')
                  || (currentStep === 'user-details' && step.id === 'testimonial')
                    ? 'bg-gradient-to-r from-violet-100 to-indigo-100 text-indigo-800 dark:from-violet-900/40 dark:to-indigo-900/40 dark:text-indigo-200'
                    : 'bg-gray-100 text-gray-500 dark:bg-gray-800',
              )}
            >
              {currentStep === 'thank-you'
              || (currentStep === 'spin-wheel' && step.id !== 'thank-you' && step.id !== 'spin-wheel')
              || (currentStep === 'user-details' && step.id === 'testimonial') ? (
                <Check className="h-5 w-5" />
                ) : step.id === 'spin-wheel' ? (
                  <Gift className="h-5 w-5" />
                ) : (
                  index + 1
                )}
            </div>

            {index < steps.length - 1 && (
              <div className="relative mx-4 w-20 h-0.5">
                <div
                  className={cn(
                    'absolute inset-0 transition-all duration-500 rounded-full',
                    (currentStep === 'user-details' && index === 0)
                    || (currentStep === 'spin-wheel' && index < 2)
                    || (currentStep === 'thank-you' && index < steps.length - 1)
                      ? 'bg-gradient-to-r from-violet-600 to-indigo-600'
                      : 'bg-gray-200 dark:bg-gray-700',
                  )}
                />
              </div>
            )}
          </div>
        ))}
      </div>
    );
  };

  // Render testimonial step
  const renderTestimonialStep = () => (
    <>
      <div className="px-8 pb-6">
        <div className="flex flex-col items-center text-center">
          <div className="space-y-3">
            <h1 className="text-3xl font-bold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-violet-600 to-indigo-600 dark:from-violet-400 dark:to-indigo-400">
              Share Your Experience
            </h1>
            <p className="text-base text-gray-600 dark:text-gray-300 max-w-md mx-auto">
              Your feedback helps us improve our product and deliver better experiences for everyone.
            </p>
          </div>

          {/* Rating */}
          <div className="flex items-center justify-center mt-8 mb-3">
            {[1, 2, 3, 4, 5].map((star) => (
              <button
                key={star}
                onClick={() => handleRating(star)}
                onMouseEnter={() => setHoverRating(star)}
                onMouseLeave={() => setHoverRating(null)}
                className="focus:outline-none px-1.5 transition-transform hover:scale-110 active:scale-95"
                aria-label={`Rate ${star} stars`}
              >
                <Star
                  className={cn(
                    'w-10 h-10 transition-all',
                    (hoverRating && hoverRating >= star) || (rating && rating >= star && !hoverRating)
                      ? 'fill-amber-400 text-amber-400 drop-shadow-md'
                      : 'text-gray-200 dark:text-gray-700',
                  )}
                />
              </button>
            ))}
          </div>

          {rating && (
          <div className="text-base font-medium text-indigo-600 dark:text-indigo-400 animate-fadeIn mt-1">
            {rating === 5
              ? 'Excellent! 🎉'
              : rating === 4
                ? 'Very Good! 👍'
                : rating === 3
                  ? 'Good 😊'
                  : rating === 2
                    ? 'Fair 🤔'
                    : 'Poor 😞'}
          </div>
          )}

          {errors.rating && (
          <div className="text-sm text-red-500 mt-2 bg-red-50 dark:bg-red-900/20 px-3 py-1 rounded-full">
            {errors.rating}
          </div>
          )}
        </div>
      </div>

      <div className="px-8 pb-8">
        {/* Custom Tabs */}
        <div className="w-full">
          <div className="flex rounded-xl bg-gray-100 dark:bg-gray-800/60 p-1 mb-6 shadow-inner">
            <button
              onClick={() => setActiveTab('text')}
              className={cn(
                'flex-1 text-sm font-medium py-2.5 rounded-lg transition-all',
                activeTab === 'text'
                  ? 'bg-white dark:bg-gray-700 text-indigo-600 dark:text-indigo-300 shadow-sm'
                  : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300',
              )}
            >
              Write a Review
            </button>
            <button
              onClick={() => setActiveTab('video')}
              className={cn(
                'flex-1 text-sm font-medium py-2.5 rounded-lg transition-all flex items-center justify-center gap-1.5',
                activeTab === 'video'
                  ? 'bg-white dark:bg-gray-700 text-indigo-600 dark:text-indigo-300 shadow-sm'
                  : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300',
              )}
            >
              <Camera className="h-4 w-4" />
              Record Video
            </button>
          </div>

          {/* Text Tab Content */}
          <div className={cn('mt-0 space-y-4', activeTab !== 'text' && 'hidden')}>
            <textarea
              placeholder="Tell us what you think about our product..."
              className="w-full min-h-[180px] resize-none rounded-xl border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 p-4 text-base focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-colors"
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
            />

            <div className="flex justify-between items-center text-xs text-gray-500 dark:text-gray-400">
              <div className="bg-gray-100 dark:bg-gray-800 px-3 py-1 rounded-full">
                {feedback.length} / 500 characters
              </div>
              {errors.feedback && (
              <div className="text-sm text-red-500 bg-red-50 dark:bg-red-900/20 px-3 py-1 rounded-full">
                {errors.feedback}
              </div>
              )}
            </div>
          </div>

          {/* Video Tab Content */}
          <div className={cn('mt-0', activeTab !== 'video' && 'hidden')}>
            <div className="relative rounded-xl overflow-hidden bg-gradient-to-br from-gray-900 to-indigo-900 aspect-video shadow-lg">
              {/* Video preview would go here in a real implementation */}
              <div className="absolute inset-0 flex items-center justify-center">
                {!isRecording && !recordingTime && (
                <div className="text-white/90 flex flex-col items-center">
                  <div className="w-20 h-20 rounded-full bg-indigo-500/20 backdrop-blur-sm flex items-center justify-center mb-3 border border-indigo-500/30">
                    <Video className="h-10 w-10" />
                  </div>
                  <span className="text-base font-medium">Record a video testimonial</span>
                  <p className="text-xs text-indigo-200 mt-2 max-w-xs text-center">
                    Share your experience in a short video to help others make informed decisions
                  </p>
                </div>
                )}
              </div>

              {/* Recording indicator */}
              {isRecording && (
              <div className="absolute top-4 right-4 bg-black/70 text-white px-3 py-1.5 rounded-full text-xs font-medium flex items-center backdrop-blur-sm">
                <span className="h-2 w-2 rounded-full bg-red-500 mr-2 animate-pulse" />
                {formatTime(recordingTime)}
              </div>
              )}

              {/* Controls */}
              <div className="absolute bottom-0 left-0 right-0 p-6 flex justify-center gap-3">
                {recordingTime > 0 && !isRecording ? (
                  <>
                    <button
                      onClick={() => {
                        setRecordingTime(0);
                      }}
                      className="rounded-full w-12 h-12 bg-white/10 border border-white/20 backdrop-blur-sm text-white hover:bg-white/20 flex items-center justify-center transition-all focus:outline-none focus:ring-2 focus:ring-white/50"
                    >
                      <X className="h-5 w-5" />
                    </button>
                    <button className="rounded-full w-12 h-12 bg-indigo-600/80 border border-indigo-500/30 backdrop-blur-sm text-white hover:bg-indigo-600 flex items-center justify-center transition-all focus:outline-none focus:ring-2 focus:ring-indigo-500">
                      <Check className="h-5 w-5" />
                    </button>
                  </>
                ) : (
                  <button
                    onClick={toggleRecording}
                    className={cn(
                      'rounded-full w-16 h-16 flex items-center justify-center transition-all focus:outline-none focus:ring-2 focus:ring-offset-2 shadow-lg',
                      isRecording
                        ? 'bg-white text-red-500 hover:bg-gray-100 focus:ring-red-500'
                        : 'bg-red-500 text-white hover:bg-red-600 focus:ring-red-500',
                    )}
                    aria-label={isRecording ? 'Pause recording' : 'Start recording'}
                  >
                    {isRecording ? <Pause className="h-6 w-6" /> : <Video className="h-6 w-6" />}
                  </button>
                )}
              </div>
            </div>

            {errors.video && (
            <div className="text-sm text-red-500 mt-3 bg-red-50 dark:bg-red-900/20 px-3 py-1.5 rounded-full text-center">
              {errors.video}
            </div>
            )}

            <div className="mt-6 text-sm text-gray-600 dark:text-gray-300 bg-gray-50 dark:bg-gray-800/50 p-4 rounded-xl">
              <h4 className="font-medium mb-2 text-indigo-600 dark:text-indigo-400">
                Tips for a great video review:
              </h4>
              <ul className="space-y-2">
                <li className="flex items-start">
                  <span className="bg-indigo-100 dark:bg-indigo-900/30 text-indigo-800 dark:text-indigo-200 w-5 h-5 rounded-full flex items-center justify-center mr-2 mt-0.5 flex-shrink-0">
                    1
                  </span>
                  <span>Keep it under 60 seconds for maximum impact</span>
                </li>
                <li className="flex items-start">
                  <span className="bg-indigo-100 dark:bg-indigo-900/30 text-indigo-800 dark:text-indigo-200 w-5 h-5 rounded-full flex items-center justify-center mr-2 mt-0.5 flex-shrink-0">
                    2
                  </span>
                  <span>Mention specific features you liked or would improve</span>
                </li>
                <li className="flex items-start">
                  <span className="bg-indigo-100 dark:bg-indigo-900/30 text-indigo-800 dark:text-indigo-200 w-5 h-5 rounded-full flex items-center justify-center mr-2 mt-0.5 flex-shrink-0">
                    3
                  </span>
                  <span>Speak clearly and face the camera for better engagement</span>
                </li>
              </ul>
            </div>

            {/* Prize eligibility notice */}
            {activeTab === 'video' && rating && rating >= 4 && (
            <div className="mt-6 p-4 bg-gradient-to-r from-amber-50 to-yellow-50 dark:from-amber-900/30 dark:to-yellow-900/30 border border-yellow-200 dark:border-yellow-900/50 rounded-xl flex items-start">
              <div className="bg-gradient-to-r from-amber-500 to-yellow-500 p-2 rounded-lg mr-3 flex-shrink-0">
                <Gift className="h-5 w-5 text-white" />
              </div>
              <div>
                <p className="font-medium text-amber-800 dark:text-amber-300">
                  You're eligible for our prize wheel!
                </p>
                <p className="text-sm text-amber-700 dark:text-amber-400 mt-1">
                  Video testimonials with 4+ star ratings qualify for exciting rewards. Complete your submission to
                  spin the wheel!
                </p>
              </div>
            </div>
            )}
          </div>
        </div>
      </div>
    </>
  );

  // Render user details step
  const renderUserDetailsStep = () => (
    <>
      <div className="px-8 pb-6 pt-2">
        <div className="flex items-center mb-4">
          <button
            className="flex items-center text-gray-500 hover:text-gray-900 dark:hover:text-gray-100 text-sm font-medium focus:outline-none group transition-colors"
            onClick={handlePrevious}
          >
            <ChevronLeft className="mr-1 h-4 w-4 transition-transform group-hover:-translate-x-0.5" />
            Back
          </button>
        </div>
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2 bg-clip-text text-transparent bg-gradient-to-r from-violet-600 to-indigo-600 dark:from-violet-400 dark:to-indigo-400">
            One more thing...{' '}
            <span role="img" aria-label="smiling face with sunglasses" className="text-3xl">
              😎
            </span>
          </h1>
        </div>
      </div>

      <div className="px-8 pb-8">
        <form className="space-y-6" onSubmit={(e) => e.preventDefault()}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Full name <span className="text-red-500">*</span>
              </label>
              <input
                id="name"
                type="text"
                placeholder="John Smith"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className={cn(
                  'w-full rounded-xl border py-3 px-4 shadow-sm focus:outline-none focus:ring-2 focus:border-transparent transition-colors',
                  errors.name
                    ? 'border-red-500 focus:ring-red-500 bg-red-50 dark:bg-red-900/10'
                    : 'border-gray-200 dark:border-gray-700 focus:ring-indigo-500 bg-blue-50 dark:bg-blue-900/10',
                  'text-gray-900 dark:text-gray-100',
                )}
              />
              {errors.name && <p className="text-sm text-red-500 mt-1">{errors.name}</p>}
            </div>

            <div className="space-y-2">
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Email <span className="text-red-500">*</span>
              </label>
              <input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className={cn(
                  'w-full rounded-xl border py-3 px-4 shadow-sm focus:outline-none focus:ring-2 focus:border-transparent transition-colors',
                  errors.email
                    ? 'border-red-500 focus:ring-red-500 bg-red-50 dark:bg-red-900/10'
                    : 'border-gray-200 dark:border-gray-700 focus:ring-indigo-500 bg-blue-50 dark:bg-blue-900/10',
                  'text-gray-900 dark:text-gray-100',
                )}
              />
              {errors.email && <p className="text-sm text-red-500 mt-1">{errors.email}</p>}
            </div>
          </div>

          <div className="space-y-2">
            <label htmlFor="website" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Website
            </label>
            <input
              id="website"
              type="text"
              placeholder="https://company.com"
              value={website}
              onChange={(e) => setWebsite(e.target.value)}
              className="w-full rounded-xl border border-gray-200 dark:border-gray-700 py-3 px-4 text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-900 shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-colors"
            />
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Profile Picture</label>
            <div className="flex items-center gap-4">
              <div className="w-16 h-16 rounded-full border-2 border-gray-200 dark:border-gray-700 flex items-center justify-center overflow-hidden bg-gray-50 dark:bg-gray-800">
                {profilePreview ? (
                  <img
                    src={profilePreview || '/placeholder.svg'}
                    alt="Profile preview"
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-8 w-8 text-gray-300 dark:text-gray-600"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                    />
                  </svg>
                )}
              </div>
              <div className="flex gap-3">
                <button
                  type="button"
                  onClick={() => fileInputRef.current?.click()}
                  className="px-4 py-2 border border-gray-200 dark:border-gray-700 rounded-lg text-sm hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500"
                >
                  Pick an image
                  <input
                    ref={fileInputRef}
                    type="file"
                    className="hidden"
                    accept="image/*"
                    onChange={handleProfilePicture}
                  />
                </button>
                {profilePreview && (
                <button
                  type="button"
                  onClick={() => {
                    setProfilePicture(null);
                    setProfilePreview(null);
                  }}
                  className="text-red-500 hover:text-red-700 text-sm font-medium"
                >
                  Remove
                </button>
                )}
              </div>
            </div>
          </div>

          {/* Progress bar */}
          <div className="mt-8">
            <div className="h-2 w-full bg-gray-100 dark:bg-gray-800 rounded-full overflow-hidden">
              <div
                className="h-full bg-gradient-to-r from-violet-500 to-indigo-500 rounded-full"
                style={{ width: '67%' }}
              />
            </div>
            <div className="flex justify-center mt-2">
              <div className="bg-indigo-100 dark:bg-indigo-900/30 text-indigo-800 dark:text-indigo-200 px-4 py-2 rounded-full text-sm font-medium flex items-center">
                <svg
                  className="animate-spin -ml-1 mr-2 h-4 w-4 text-indigo-600 dark:text-indigo-400"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  />
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  />
                </svg>
                67% Complete
              </div>
            </div>
          </div>

          {/* Prize eligibility notice */}
          {eligibleForPrize && (
          <div className="p-4 bg-gradient-to-r from-amber-50 to-yellow-50 dark:from-amber-900/30 dark:to-yellow-900/30 border border-yellow-200 dark:border-yellow-900/50 rounded-xl flex items-start">
            <div className="bg-gradient-to-r from-amber-500 to-yellow-500 p-2 rounded-lg mr-3 flex-shrink-0">
              <Gift className="h-5 w-5 text-white" />
            </div>
            <div>
              <p className="font-medium text-amber-800 dark:text-amber-300">You're eligible for our prize wheel!</p>
              <p className="text-sm text-amber-700 dark:text-amber-400 mt-1">
                Complete this form to spin the wheel and win exciting prizes.
              </p>
            </div>
          </div>
          )}
        </form>
      </div>
    </>
  );

  // Render spin wheel step
  const renderSpinWheelStep = () => (
    <>
      <div className="px-8 pb-6 pt-2">
        <div className="flex items-center mb-4">
          <button
            className="flex items-center text-gray-500 hover:text-gray-900 dark:hover:text-gray-100 text-sm font-medium focus:outline-none group transition-colors"
            onClick={handlePrevious}
          >
            <ChevronLeft className="mr-1 h-4 w-4 transition-transform group-hover:-translate-x-0.5" />
            Back
          </button>
        </div>
        <div className="text-center">
          <h1 className="text-3xl font-bold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-amber-500 to-yellow-500 dark:from-amber-400 dark:to-yellow-400">
            Spin to Win!
          </h1>
          <p className="text-base text-gray-600 dark:text-gray-300 mt-2 max-w-md mx-auto">
            Thanks for your video testimonial! Spin the wheel to claim your prize.
          </p>
        </div>
      </div>

      <div className="px-8 pb-8">
        {/* <SpinWheel prizes={prizes} onComplete={handleSpinComplete} /> */}
      </div>
    </>
  );

  // Render thank you step
  const renderThankYouStep = () => (
    <>
      <div className="px-8 pb-6 pt-10">
        <div className="flex flex-col items-center text-center">
          <div className="w-20 h-20 bg-gradient-to-br from-indigo-100 to-violet-100 dark:from-indigo-900/40 dark:to-violet-900/40 rounded-full flex items-center justify-center mb-6 animate-bounce shadow-md">
            <Check className="h-10 w-10 text-indigo-600 dark:text-indigo-400" />
          </div>
          <h1 className="text-3xl font-bold tracking-tight animate-fadeIn bg-clip-text text-transparent bg-gradient-to-r from-violet-600 to-indigo-600 dark:from-violet-400 dark:to-indigo-400">
            Thank You!
          </h1>
          <p className="text-base text-gray-600 dark:text-gray-300 mt-3 max-w-md animate-fadeIn delay-150">
            We appreciate you taking the time to share your experience with us. Your feedback helps us improve and
            deliver better experiences for everyone.
          </p>
        </div>
      </div>

      <div className="px-8 pb-8 text-center">
        <div className="bg-gradient-to-br from-gray-50 to-indigo-50 dark:from-gray-800/50 dark:to-indigo-900/20 rounded-xl p-6 mb-6 animate-fadeIn delay-300 shadow-sm">
          <div className="flex justify-center mb-3">
            {[1, 2, 3, 4, 5].map((star) => (
              <Star
                key={star}
                className={cn(
                  'w-6 h-6',
                  rating && rating >= star ? 'fill-amber-400 text-amber-400' : 'text-gray-300 dark:text-gray-600',
                )}
              />
            ))}
          </div>

          {activeTab === 'text' ? (
            <p className="text-base text-gray-700 dark:text-gray-300 italic">"{feedback}"</p>
          ) : (
            <div className="flex items-center justify-center gap-2 text-gray-700 dark:text-gray-300">
              <Video className="h-5 w-5 text-indigo-500" />
              <p>Video testimonial recorded</p>
            </div>
          )}
        </div>

        {/* Show prize if won */}
        {wonPrize && (
        <div className="bg-gradient-to-br from-amber-50 to-yellow-50 dark:from-amber-900/30 dark:to-yellow-900/30 border border-yellow-200 dark:border-yellow-900/50 rounded-xl p-6 mb-6 animate-fadeIn delay-500 shadow-sm">
          <h3 className="font-medium text-amber-800 dark:text-amber-300 mb-3 flex items-center justify-center text-lg">
            <Gift className="h-5 w-5 mr-2" />
            Your Prize
          </h3>

          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 mb-4 shadow-sm">
            <p className="font-bold text-xl text-amber-700 dark:text-amber-300">{wonPrize.text}</p>
          </div>

          {wonPrize.type === 'coupon' && (
          <div className="bg-gray-100 dark:bg-gray-700 p-3 rounded-lg font-mono text-base mb-4 border border-dashed border-amber-300 dark:border-amber-700">
            {wonPrize.value}
          </div>
          )}

          {wonPrize.type === 'link' && (
          <a
            href={wonPrize.value}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-block bg-gradient-to-r from-amber-500 to-yellow-500 hover:from-amber-600 hover:to-yellow-600 text-white px-5 py-3 rounded-lg text-base font-medium transition-all shadow-sm hover:shadow"
          >
            Claim Your Prize
          </a>
          )}
        </div>
        )}

        <p className="text-sm text-gray-500 dark:text-gray-400 animate-fadeIn delay-700">
          Your testimonial will be reviewed and may be featured on our website.
        </p>
      </div>
    </>
  );

  // Get the content for the current step
  const getStepContent = (step) => {
    switch(step) {
      case 'testimonial':
        return renderTestimonialStep();
      case 'user-details':
        return renderUserDetailsStep();
      case 'spin-wheel':
        return renderSpinWheelStep();
      case 'thank-you':
        return renderThankYouStep();
      default:
        return null;
    }
  };

  // Render footer buttons based on current step
  const renderFooterButtons = () => {
    if(currentStep === 'thank-you') {
      return (
        <button
          className="w-full bg-gradient-to-r from-violet-600 to-indigo-600 hover:from-violet-700 hover:to-indigo-700 text-white font-medium py-3.5 px-4 rounded-xl transition-all focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 animate-fadeIn delay-700 shadow-sm hover:shadow"
          onClick={() => {
            // In a real app, you might redirect to home page or another section
            window.location.reload();
          }}
        >
          Return Home
        </button>
      );
    }

    if(currentStep === 'spin-wheel') {
      return null; // No footer buttons on spin wheel page
    }

    return (
      <div className="flex flex-col w-full">
        <button
          className={cn(
            'w-full text-white font-medium py-3.5 px-4 rounded-xl transition-all focus:outline-none focus:ring-2 focus:ring-offset-2 shadow-sm hover:shadow flex items-center justify-center',
            currentStep === 'user-details'
              ? 'bg-gradient-to-r from-violet-600 to-indigo-600 hover:from-violet-700 hover:to-indigo-700 focus:ring-indigo-500'
              : 'bg-gradient-to-r from-violet-600 to-indigo-600 hover:from-violet-700 hover:to-indigo-700 focus:ring-indigo-500',
          )}
          onClick={handleNext}
        >
          {currentStep === 'user-details' ? 'Submit' : 'Continue'}
          <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-0.5" />
        </button>

        {currentStep === 'user-details' && (
          <p className="text-center text-xs text-gray-500 dark:text-gray-400 mt-4">
            By submitting your feedback, you agree to our terms, privacy policy, and grant permission for its use across
            social channels and in our marketing efforts.
          </p>
        )}
      </div>
    );
  };

  return (
    <div className="w-full max-w-2xl mx-auto bg-white dark:bg-gray-900 rounded-2xl shadow-xl overflow-hidden border border-gray-100 dark:border-gray-800">
      {renderProgress()}

      <div className="relative overflow-hidden">
        <div className={cn('transition-all duration-300 ease-in-out transform', getAnimationClasses())}>
          {getStepContent(visibleStep)}
        </div>
      </div>

      <div className="px-8 pb-8 pt-0">{renderFooterButtons()}</div>

      {/* Confetti animation */}
      {showConfetti && <Confetti />}
    </div>
  );
}

function Confetti({ duration = 3000, particleCount = 150, speed = 3, spread = 70 }) {
  const canvasRef = useRef(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if(!canvas) {
      return;
    }

    const ctx = canvas.getContext('2d');
    if(!ctx) {
      return;
    }

    // Set canvas to full window size
    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Confetti particles
    const particles = [];

    // Confetti colors
    const colors = [
      '#FFC700', // yellow
      '#FF0000', // red
      '#2BD1FC', // cyan
      '#C04CFD', // purple
      '#FF6B6B', // pink
      '#38B000', // green
    ];

    // Particle class
    class Particle {
      constructor() {
        this.x = Math.random() * canvas.width;
        this.y = -20 - Math.random() * 100; // Start above the canvas
        this.color = colors[Math.floor(Math.random() * colors.length)];
        this.size = Math.random() * 10 + 5;
        this.speed = Math.random() * speed + 2;
        this.angle = Math.random() * spread - spread / 2;
        this.rotation = Math.random() * 360;
        this.rotationSpeed = Math.random() * 10 - 5;

        // Randomly choose shape
        const shapes = ['circle', 'square', 'triangle'];
        this.shape = shapes[Math.floor(Math.random() * shapes.length)];
      }

      update() {
        this.y += this.speed;
        this.x += Math.sin(this.angle * (Math.PI / 180)) * 2;
        this.rotation += this.rotationSpeed;

        // Reset particle if it goes off screen
        if(this.y > canvas.height + 20) {
          this.y = -20;
          this.x = Math.random() * canvas.width;
        }
      }

      draw() {
        if(!ctx) {
          return;
        }

        ctx.save();
        ctx.translate(this.x, this.y);
        ctx.rotate(this.rotation * (Math.PI / 180));
        ctx.fillStyle = this.color;

        if(this.shape === 'circle') {
          ctx.beginPath();
          ctx.arc(0, 0, this.size / 2, 0, Math.PI * 2);
          ctx.fill();
        } else if(this.shape === 'square') {
          ctx.fillRect(-this.size / 2, -this.size / 2, this.size, this.size);
        } else if(this.shape === 'triangle') {
          ctx.beginPath();
          ctx.moveTo(0, -this.size / 2);
          ctx.lineTo(-this.size / 2, this.size / 2);
          ctx.lineTo(this.size / 2, this.size / 2);
          ctx.closePath();
          ctx.fill();
        }

        ctx.restore();
      }
    }

    // Create particles
    for(let i = 0; i < particleCount; i++) {
      particles.push(new Particle());
    }

    // Animation loop
    let animationId;
    const startTime = Date.now();

    const animate = () => {
      if(!ctx) {
        return;
      }

      // Clear canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Update and draw particles
      for(const particle of particles) {
        particle.update();
        particle.draw();
      }

      // Check if animation should continue
      if(duration === 0 || Date.now() - startTime < duration) {
        animationId = requestAnimationFrame(animate);
      } else {
        // Fade out canvas when animation ends
        const fadeOut = () => {
          if(!ctx) {
            return;
          }

          ctx.globalAlpha -= 0.05;
          ctx.clearRect(0, 0, canvas.width, canvas.height);

          for(const particle of particles) {
            particle.draw();
          }

          if(ctx.globalAlpha > 0) {
            requestAnimationFrame(fadeOut);
          } else {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
          }
        };

        fadeOut();
      }
    };

    // Start animation
    animationId = requestAnimationFrame(animate);

    // Cleanup
    return () => {
      window.removeEventListener('resize', resizeCanvas);
      cancelAnimationFrame(animationId);
    };
  }, [duration, particleCount, speed, spread]);

  return <canvas ref={canvasRef} className="fixed inset-0 pointer-events-none z-50" aria-hidden="true" />;
}
