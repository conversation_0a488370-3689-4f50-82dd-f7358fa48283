{"name": "shapo-frontend", "version": "3.0.0", "private": true, "scripts": {"dev": "next dev -p 4000", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "eslint . --fix", "prepare": "husky || true"}, "lint-staged": {"*.js": ["eslint --fix"]}, "lint-staged:quiet": {"*.js": ["eslint --fix --quiet"]}, "dependencies": {"@analytics/google-tag-manager": "0.5.5", "@headlessui/react": "1.7.15", "@ramonak/react-progress-bar": "^5.0.3", "@sentry/nextjs": "^7.120.3", "@tailwindcss/aspect-ratio": "0.2.1", "@tailwindcss/line-clamp": "0.4.4", "@tiptap/extension-hard-break": "2.1.12", "@tiptap/extension-highlight": "2.6.6", "@tiptap/extension-placeholder": "^2.4.0", "@tiptap/extension-underline": "2.4.0", "@tiptap/pm": "^2.7.0", "@tiptap/react": "2.1.12", "@tiptap/starter-kit": "2.1.12", "a11y-react-emoji": "1.1.3", "analytics": "0.8.9", "array-move": "4.0.0", "axios": "0.25.0", "body-scroll-lock": "4.0.0-beta.0", "cookies-next": "^2.1.2", "core-js": "3.30.2", "currency-symbol-map": "^5.1.0", "email-validator": "^2.0.4", "hls.js": "^1.5.13", "html-to-image": "1.11.11", "install": "0.13.0", "is-mobile": "^4.0.0", "is-rtl-text": "0.0.2", "linkify-html": "4.1.3", "lint-staged": "^15.4.3", "lodash": "4.17.21", "lucide-react": "^0.475.0", "mixpanel-browser": "2.56.0", "moment": "2.29.1", "next": "12.0.8", "next-seo": "6.1.0", "npm": "10.2.1", "nprogress": "0.2.0", "papaparse": "^5.4.1", "posthog-js": "^1.242.2", "rc-slider": "^10.5.0", "react": "17.0.2", "react-avatar": "5.0.3", "react-countdown": "2.3.6", "react-datepicker": "^4.15.0", "react-dom": "17.0.2", "react-easy-sort": "1.6.0", "react-fast-marquee": "1.6.5", "react-fontpicker-ts": "^1.1.0", "react-hook-form": "7.45.0", "react-hot-toast": "2.1.1", "react-qr-code": "^2.0.15", "react-responsive-masonry": "2.1.7", "react-star-ratings": "^2.3.0", "react-step-wizard": "5.3.9", "react-syntax-highlighter": "15.5.0", "react-timeago": "6.2.1", "react-tooltip": "5.21.5", "react-use-intercom": "5.4.1", "react-webcam": "^7.2.0", "simplebar": "^6.3.0", "simplebar-react": "^3.3.0", "swiper": "^8.4.7", "swr": "1.1.2", "ua-parser-js": "^1.0.38", "use-analytics": "^1.1.0", "validator": "^13.11.0"}, "devDependencies": {"@next/eslint-plugin-next": "^12.0.8", "autoprefixer": "10.4.2", "eslint": "^8.57.1", "eslint-config-airbnb": "^19.0.4", "eslint-config-next": "^12.0.8", "eslint-plugin-import": "^2.31.0", "husky": "^9.1.7", "lint-staged": "^15.4.3", "node-ssh": "^13.1.0", "postcss": "8.4.5", "simple-git": "^3.19.1", "tailwindcss": "2.2.19"}}