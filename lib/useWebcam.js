// useWebcam.js
import { useState, useRef, useEffect, useCallback } from 'react';
import Webcam from 'react-webcam';
import userAgentUtils from './userAgentUtils';

const useWebcam = ({ captureLimitInSeconds, countDownInSeconds, permissionAutoStart }) => {
  const webcamRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  const [capturing, setCapturing] = useState(false);
  const [recordedChunks, setRecordedChunks] = useState([]);
  const [captureDuration, setCaptureDuration] = useState(0);
  const [timerId, setTimerId] = useState(null);
  const [previewUrl, setPreviewUrl] = useState(null);
  const [recordedBlob, setRecordedBlob] = useState(null);
  const [isCameraLoading, setIsCameraLoading] = useState(true);
  const [countdown, setCountdown] = useState(countDownInSeconds);
  const [showCountdown, setShowCountdown] = useState(false);
  const [hasPermissions, setHasPermissions] = useState(null);

  const userAgentData = userAgentUtils.parseUserAgent(navigator.userAgent);
  const BrowserTypes = {
    Chrome: 'Chrome',
    Firefox: 'Firefox',
    // Opera: 'Opera',
    // Yandex: 'Yandex',
    Safari: 'Safari',
    // InternetExplorer: 'Internet Explorer',
    // Edge: 'Edge',
    // Chromium: 'Chromium',
    // Ie: 'IE',
    // MobileSafari: 'Mobile Safari',
    // EdgeChromium: 'Edge Chromium',
    // MIUI: 'MIUI Browser',
    // SamsungBrowser: 'Samsung Browser',
  };

  const formatDuration = (duration) => {
    if(duration === 0) {
      return '00:00';
    }
    const minutes = Math.floor(duration / 60)
      .toString()
      .padStart(2, '0');
    const seconds = (duration % 60).toString().padStart(2, '0');
    return `${minutes}:${seconds}`;
  };

  const handleCameraLoaded = () => {
    setIsCameraLoading(false);
  };

  const startCapture = useCallback(() => {
    setCaptureDuration(0);
    setRecordedChunks([]);
    setPreviewUrl(null);
    setShowCountdown(true);
    setCountdown(countDownInSeconds);

    const countdownStart = Date.now();
    const countdownStep = () => {
      const elapsed = Math.floor((Date.now() - countdownStart) / 1000);
      const newCountdown = countDownInSeconds - elapsed;
      setCountdown(newCountdown);

      if(newCountdown > 0) {
        requestAnimationFrame(countdownStep);
      } else {
        setShowCountdown(false);
        startRecording();
      }
    };

    requestAnimationFrame(countdownStep);

    const startRecording = () => {
      let mimeType = 'video/webm;codecs=vp9';
      if(userAgentData.browser.name === BrowserTypes.Safari) {
        mimeType = 'video/mp4';
      }
      if(userAgentData.browser.name === BrowserTypes.Firefox) {
        mimeType = 'video/webm';
      }
      mediaRecorderRef.current = new MediaRecorder(webcamRef.current.stream, {
        mimeType,
      });
      mediaRecorderRef.current.start();
      setCapturing(true);
      const id = setInterval(() => {
        setCaptureDuration((prevDuration) => prevDuration + 1);
      }, 1000);
      setTimerId(id);
      setTimeout(async () => {
        clearInterval(id);
        await stopCapture();
      }, captureLimitInSeconds * 1000);
    };
  }, [
    webcamRef,
    setCapturing,
    setCaptureDuration,
    setRecordedChunks,
    mediaRecorderRef,
    captureLimitInSeconds,
    countDownInSeconds,
  ]);

  const updateVideoPreview = (data) => {
    const blob = new Blob([data], { type: 'video/mp4' });
    const url = URL.createObjectURL(blob);
    setPreviewUrl(url);
  };

  const resetCapture = () => {
    setCaptureDuration(0);
    setRecordedChunks([]);
    setRecordedBlob(null);
    setPreviewUrl(null);
  };

  const stopCapture = useCallback(async () => {
    if(mediaRecorderRef.current) {
      const handleDataAvailable = ({ data }) => {
        if(data.size > 0) {
          setRecordedChunks((prev) => prev.concat(data));
          let blobMimeType = 'video/webm;codecs=vp9';
          if(userAgentData.browser.name === BrowserTypes.Safari) {
            blobMimeType = 'video/mp4';
          } else if(userAgentData.browser.name === BrowserTypes.Firefox) {
            blobMimeType = 'video/webm';
          }
          setRecordedBlob(new Blob([data], { type: blobMimeType }));
          updateVideoPreview(data);
        }
      };
      mediaRecorderRef.current.addEventListener('dataavailable', handleDataAvailable);
      mediaRecorderRef.current.stop();
      await new Promise((resolve) => {
        mediaRecorderRef.current.addEventListener('stop', resolve);
      });
      mediaRecorderRef.current.removeEventListener('dataavailable', handleDataAvailable);
      setCapturing(false);
      setCaptureDuration(0);
      clearInterval(timerId);
      setTimerId(null);
    }
  }, [mediaRecorderRef, capturing, setCapturing, setCaptureDuration, timerId]);

  const requestPermissions = async () => {
    try {
      await navigator.mediaDevices.getUserMedia({ video: true, audio: true });
      setHasPermissions(true);
    } catch(error) {
      if(error.message.includes('Permission denied')) {
        setHasPermissions(false);
      }
    }
  };

  useEffect(() => {
    if(permissionAutoStart) {
      requestPermissions();
    }
  }, []);

  return {
    WebcamComponent: <Webcam audio muted ref={webcamRef} onUserMedia={handleCameraLoaded} />,
    duration: formatDuration(captureDuration),
    startCapture,
    stopCapture,
    resetCapture,
    capturing,
    recordedChunks,
    recordedBlob,
    previewUrl,
    isCameraLoading,
    countdown,
    showCountdown,
    hasPermissions,
    requestPermissions,
    BrowserTypes,
    userAgentData,
  };
};

export default useWebcam;
