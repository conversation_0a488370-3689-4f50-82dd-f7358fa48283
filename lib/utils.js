import linkifyHtml from 'linkify-html';

module.exports = {
  transformMessage(text) {
    return text
      ? linkifyHtml(text, {
        attributes: { target: '_blank', rel: 'noindex, nofollow' },
        className: 'font-semibold underline',

        validate: {
          url: (value) => value.includes('http') },
      })
      : '';
  },
  fileToBase64(file) {
    return new Promise((resolve, reject) => {
      const singleFile = file[0] || file;
      const reader = new FileReader();
      reader.readAsDataURL(singleFile);
      reader.onload = () => resolve(reader.result.toString());
      reader.onerror = (error) => reject(error);
    });
  },
  isDarkColor(hex) {
    if(!hex || hex === '') {
      return false;
    }
    hex = hex.replace('#', '');

    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);

    const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    return brightness < 128;
  },
  shouldRenderWidget(data) {
    if(!data) {
      return false;
    }
    if(data.widget?.type === 'BadgeWidget') {
      return data.totals?.total > 0;
    }
    return data.testimonials?.length > 0;
  },

};
