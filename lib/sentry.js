import * as Sentry from "@sentry/nextjs";

// Get the Sentry logger (available after enabling logs in configuration)
export const logger = Sentry.logger || console;

/**
 * Capture an exception with additional context
 * @param {Error} error - The error to capture
 * @param {Object} context - Additional context (tags, extra data, user info)
 */
export function captureException(error, context = {}) {
  return Sentry.captureException(error, {
    tags: context.tags || {},
    extra: context.extra || {},
    user: context.user || {},
    level: context.level || 'error',
  });
}

/**
 * Create a performance span for UI interactions
 * @param {string} name - Descriptive name for the operation
 * @param {Function} operation - The operation to track
 * @param {Object} attributes - Additional attributes to add to the span
 */
export async function trackUIAction(name, operation, attributes = {}) {
  return Sentry.startSpan(
    {
      op: "ui.action",
      name: name,
    },
    async (span) => {
      // Add custom attributes
      Object.entries(attributes).forEach(([key, value]) => {
        span.setAttribute(key, value);
      });
      
      try {
        const result = await operation(span);
        span.setStatus({ code: 2, message: "success" });
        return result;
      } catch (error) {
        span.setStatus({ code: 2, message: "error" });
        captureException(error, {
          tags: { operation: name },
          extra: attributes
        });
        throw error;
      }
    }
  );
}

/**
 * Create a performance span for API calls
 * @param {string} method - HTTP method (GET, POST, etc.)
 * @param {string} url - API endpoint URL
 * @param {Function} operation - The API call operation
 * @param {Object} metadata - Additional metadata about the request
 */
export async function trackAPICall(method, url, operation, metadata = {}) {
  return Sentry.startSpan(
    {
      op: "http.client",
      name: `${method} ${url}`,
    },
    async (span) => {
      span.setAttribute("http.method", method);
      span.setAttribute("http.url", url);
      
      // Add custom metadata
      Object.entries(metadata).forEach(([key, value]) => {
        span.setAttribute(key, value);
      });
      
      try {
        const result = await operation(span);
        
        // If result has response info, add it to span
        if (result && result.status) {
          span.setAttribute("http.status_code", result.status);
        }
        
        span.setStatus({ code: 2, message: "success" });
        return result;
      } catch (error) {
        span.setStatus({ code: 2, message: "error" });
        captureException(error, {
          tags: { 
            operation: `${method} ${url}`,
            api_call: true 
          },
          extra: metadata
        });
        throw error;
      }
    }
  );
}

/**
 * Create a performance span for database operations
 * @param {string} operation - Database operation (SELECT, INSERT, etc.)
 * @param {string} table - Table name
 * @param {Function} dbOperation - The database operation
 * @param {Object} metadata - Additional metadata about the query
 */
export async function trackDBOperation(operation, table, dbOperation, metadata = {}) {
  return Sentry.startSpan(
    {
      op: "db.query",
      name: `${operation} ${table}`,
    },
    async (span) => {
      span.setAttribute("db.operation", operation);
      span.setAttribute("db.table", table);
      
      // Add custom metadata
      Object.entries(metadata).forEach(([key, value]) => {
        span.setAttribute(key, value);
      });
      
      try {
        const result = await dbOperation(span);
        
        // Add result metadata if available
        if (result && typeof result === 'object') {
          if (Array.isArray(result)) {
            span.setAttribute("db.rows_affected", result.length);
          } else if (result.count !== undefined) {
            span.setAttribute("db.rows_affected", result.count);
          }
        }
        
        span.setStatus({ code: 2, message: "success" });
        return result;
      } catch (error) {
        span.setStatus({ code: 2, message: "error" });
        captureException(error, {
          tags: { 
            operation: `${operation} ${table}`,
            database: true 
          },
          extra: metadata
        });
        throw error;
      }
    }
  );
}

/**
 * Log structured messages with appropriate levels
 */
export const log = {
  trace: (message, data = {}) => logger.trace(message, data),
  debug: (message, data = {}) => logger.debug(message, data),
  info: (message, data = {}) => logger.info(message, data),
  warn: (message, data = {}) => logger.warn(message, data),
  error: (message, data = {}) => logger.error(message, data),
  fatal: (message, data = {}) => logger.fatal(message, data),
};

/**
 * Set user context for Sentry
 * @param {Object} user - User information
 */
export function setUser(user) {
  Sentry.setUser(user);
}

/**
 * Add tags to the current scope
 * @param {Object} tags - Tags to add
 */
export function setTags(tags) {
  Sentry.setTags(tags);
}

/**
 * Add extra context to the current scope
 * @param {Object} extra - Extra context to add
 */
export function setExtra(extra) {
  Sentry.setExtras(extra);
}

export default Sentry;
