import mixpanel from 'mixpanel-browser';
import posthog from 'posthog-js';

class AnalyticsTracker {
  constructor() {
    this.mixpanelInitialized = false;
    this.posthogInitialized = false;
    const enabled = process.env.NEXT_PUBLIC_ENABLE_THIRD_PARTIES === 'true';
    this.providers = {
      mixpanel: enabled,
      posthog: enabled,
    };
  }

  init({ mixpanelToken, posthogKey }) {
    if(this.providers.mixpanel && !this.mixpanelInitialized) {
      if(typeof window !== 'undefined' && window.addEventListener) {
        try {
          mixpanel.init(mixpanelToken, {
            track_pageview: 'url-with-path',
            debug: process.env.NODE_ENV !== 'production',
            ignore_dnt: true,
          });
          this.mixpanelInitialized = true;
        } catch(error) {
          console.error('Mixpanel initialization failed:', error);
        }
      }
    }
    if(this.providers.posthog && !this.posthogInitialized) {
      if(typeof window !== 'undefined') {
        try {
          posthog.init(posthogKey, {
            api_host: process.env.NEXT_PUBLIC_POSTHOG_HOST,
          });
          this.posthogInitialized = true;
        } catch(error) {
          console.error('PostHog initialization failed:', error);
        }
      }
    }
  }

  trackEvent(eventName, properties = {}) {
    if(this.mixpanelInitialized) {
      mixpanel.track(eventName, properties);
    } else if(this.providers.mixpanel) {
      console.log('[Mixpanel Tracker OFF]', eventName, properties);
    }
    if(this.posthogInitialized) {
      posthog.capture(eventName, properties);
    } else if(this.providers.posthog) {
      console.log('[PostHog Tracker OFF]', eventName, properties);
    }
  }

  identify(userId, userProperties = {}) {
    if(this.mixpanelInitialized) {
      mixpanel.identify(userId);
      if(Object.keys(userProperties).length) {
        mixpanel.people.set(userProperties);
      }
    } else if(this.providers.mixpanel) {
      console.log('[Mixpanel Tracker OFF]', userId, userProperties);
    }
    if(this.posthogInitialized) {
      posthog.identify(userId, userProperties);
    } else if(this.providers.posthog) {
      console.log('[PostHog Tracker OFF]', userId, userProperties);
    }
  }

  alias(userId) {
    if(this.mixpanelInitialized) {
      mixpanel.alias(userId);
    } else if(this.providers.mixpanel) {
      console.log('[Mixpanel Tracker OFF] alias', userId);
    }
  }

  setUserProperties(properties) {
    if(this.mixpanelInitialized) {
      mixpanel.people.set(properties);
    }
    // Recommended way to set properties in posthog
    // Via posthog.identify(distinct_id, properties) (most common, and what you already do in your identify method).
    // Or, using posthog.setPersonProperties($set, $set_once) (for explicit property updates).
    if(this.posthogInitialized) {
      posthog.setPersonProperties(properties);
    }
  }

  reset() {
    if(this.mixpanelInitialized) {
      mixpanel.reset();
    }
    if(this.posthogInitialized) {
      posthog.reset();
    }
  }
}

const tracker = new AnalyticsTracker();

export default tracker;
