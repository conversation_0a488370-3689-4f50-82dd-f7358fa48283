# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

package-lock.json
# dependencies
/node_modules
/.pnp
.pnp.js
.idea
.idea/*

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.production
.env.staging.local
.env.staging

# vercel
.vercel
/shell/.env.js
prodConfig.js

# Sentry Config File
.env.sentry-build-plugin
